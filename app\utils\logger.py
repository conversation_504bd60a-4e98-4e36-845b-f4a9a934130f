import os
import logging
import datetime
from logging.handlers import RotatingFileHandler
import traceback
import inspect

class Logger:
    """
    全局日誌系統，負責記錄系統操作和錯誤
    提供不同級別的日誌記錄：DEBUG, INFO, WARNING, ERROR, CRITICAL
    自動將日誌保存到文件中，並支持日誌檔案輪換
    """
    
    _instance = None
    
    # 單例模式
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        # 避免重複初始化
        if self._initialized:
            return
            
        self._initialized = True
        
        # 確保日誌目錄存在
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 設置日誌檔案名稱，包含當前日期
        current_date = datetime.datetime.now().strftime("%Y%m%d")
        log_file = f"{log_dir}/hb_system_{current_date}.log"
        
        # 配置日誌格式
        log_format = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(module)s - %(funcName)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 創建檔案處理器，限制單個日誌文件大小為5MB，最多保留10個備份
        file_handler = RotatingFileHandler(
            log_file, maxBytes=5*1024*1024, backupCount=10, encoding='utf-8'
        )
        file_handler.setFormatter(log_format)
        
        # 創建控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(log_format)
        
        # 配置根日誌記錄器
        self.logger = logging.getLogger('hb_system')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 記錄系統啟動信息
        self.info("系統日誌初始化完成")
    
    def _get_caller_info(self):
        """獲取調用者信息，用於詳細日誌記錄"""
        stack = inspect.stack()
        # stack[0]是當前函數，stack[1]是直接調用的日誌函數，stack[2]是實際的調用者
        if len(stack) >= 3:
            frame = stack[2]
            return f"{os.path.basename(frame.filename)}:{frame.lineno}"
        return "unknown"
    
    def debug(self, message):
        """記錄調試信息"""
        caller = self._get_caller_info()
        self.logger.debug(f"[{caller}] {message}")
    
    def info(self, message):
        """記錄一般信息"""
        caller = self._get_caller_info()
        self.logger.info(f"[{caller}] {message}")
    
    def warning(self, message):
        """記錄警告信息"""
        caller = self._get_caller_info()
        self.logger.warning(f"[{caller}] {message}")
    
    def error(self, message, exc_info=None):
        """記錄錯誤信息，可選擇是否包含異常詳情"""
        caller = self._get_caller_info()
        if exc_info:
            self.logger.error(f"[{caller}] {message}\n{traceback.format_exc()}")
        else:
            self.logger.error(f"[{caller}] {message}")
    
    def critical(self, message, exc_info=None):
        """記錄嚴重錯誤信息，可選擇是否包含異常詳情"""
        caller = self._get_caller_info()
        if exc_info:
            self.logger.critical(f"[{caller}] {message}\n{traceback.format_exc()}")
        else:
            self.logger.critical(f"[{caller}] {message}")
    
    def log_user_operation(self, username, operation, details=None):
        """記錄用戶操作，用於審計日誌"""
        details_str = f", 詳情: {details}" if details else ""
        self.info(f"用戶操作: {username} 執行了 {operation}{details_str}")
    
    def log_db_operation(self, operation, table, record_id=None):
        """記錄數據庫操作"""
        id_str = f", ID: {record_id}" if record_id else ""
        self.debug(f"數據庫操作: {operation} 資料表 {table}{id_str}")


# 方便全局訪問的實例
system_logger = Logger() 