"""
統一錯誤處理工具類
提供標準化的錯誤處理和用戶友好的錯誤信息顯示
"""

from PyQt5.QtWidgets import QMessageBox
from app.utils.logger import system_logger
from app.utils.language_manager import LanguageManager
import traceback
import functools

class ErrorHandler:
    """統一錯誤處理類"""
    
    def __init__(self):
        self.lang_manager = LanguageManager()
    
    @staticmethod
    def handle_database_error(func):
        """數據庫操作錯誤處理裝飾器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                system_logger.error(f"數據庫操作錯誤 in {func.__name__}: {str(e)}", exc_info=True)
                return None
        return wrapper
    
    @staticmethod
    def handle_ui_error(parent=None):
        """UI操作錯誤處理裝飾器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    system_logger.error(f"UI操作錯誤 in {func.__name__}: {str(e)}", exc_info=True)
                    
                    # 顯示用戶友好的錯誤信息
                    lang_manager = LanguageManager()
                    QMessageBox.critical(
                        parent,
                        lang_manager.get_text('error'),
                        f"{lang_manager.get_text('operation_failed')}\n{str(e)}"
                    )
                    return None
            return wrapper
        return decorator
    
    @staticmethod
    def show_error_dialog(parent, title, message, details=None):
        """顯示錯誤對話框"""
        lang_manager = LanguageManager()
        
        if details:
            system_logger.error(f"{title}: {message}\n詳細信息: {details}")
        else:
            system_logger.error(f"{title}: {message}")
        
        QMessageBox.critical(parent, title, message)
    
    @staticmethod
    def show_warning_dialog(parent, title, message):
        """顯示警告對話框"""
        system_logger.warning(f"{title}: {message}")
        QMessageBox.warning(parent, title, message)
    
    @staticmethod
    def show_info_dialog(parent, title, message):
        """顯示信息對話框"""
        system_logger.info(f"{title}: {message}")
        QMessageBox.information(parent, title, message)
    
    @staticmethod
    def show_confirm_dialog(parent, title, message):
        """顯示確認對話框"""
        system_logger.info(f"確認對話框: {title}: {message}")
        reply = QMessageBox.question(
            parent, 
            title, 
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes
    
    @staticmethod
    def log_user_operation(user, operation, details=""):
        """記錄用戶操作"""
        system_logger.info(f"用戶操作 - 用戶: {user}, 操作: {operation}, 詳情: {details}")
    
    @staticmethod
    def safe_execute(func, default_return=None, error_message="操作失敗"):
        """安全執行函數，捕獲所有異常"""
        try:
            return func()
        except Exception as e:
            system_logger.error(f"{error_message}: {str(e)}", exc_info=True)
            return default_return

# 全局錯誤處理器實例
error_handler = ErrorHandler() 