2025-05-28 10:04:14 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: output/Quotation_HTML_HTML-Q25040224040.html
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: output/Quotation_HTML_HTML-TEST-001.html
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:65] 成功生成發票HTML: output/Invoice_HTML_HTML-INV-001.html
2025-05-28 10:04:14 [INFO] logger - info: [pdf_generator_html.py:85] 成功生成送貨單HTML: output/DeliveryNote_HTML_HTML-DN-001.html
2025-05-28 10:45:46 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 10:45:46 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:45:46 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: output/Quotation_HTML_OPT-Q25052801.html
2025-05-28 10:45:46 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:45:46 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: output/Quotation_HTML_MULTI-Q25052802.html
2025-05-28 10:51:32 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 10:51:32 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:51:32 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: output/Quotation_HTML_HTML-TEST-001.html
2025-05-28 10:51:32 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 10:51:32 [INFO] logger - info: [pdf_generator_html.py:85] 成功生成送貨單HTML: output/DeliveryNote_HTML_HTML-DN-001.html
2025-05-28 11:00:28 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:33] 數據庫管理器初始化，數據庫路徑: database_file/app_data.db
2025-05-28 11:00:29 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:29 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:29 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:155] 開始初始化數據庫
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:229] 數據庫初始化完成
2025-05-28 11:00:29 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:29 [INFO] logger - info: [db_manager.py:33] 數據庫管理器初始化，數據庫路徑: database_file/app_data.db
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:537] 嘗試連接資料庫...
2025-05-28 11:00:31 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:31 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:540] 資料庫連接成功，初始化資料庫...
2025-05-28 11:00:31 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:31 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:31 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:31 [INFO] logger - info: [db_manager.py:155] 開始初始化數據庫
2025-05-28 11:00:31 [INFO] logger - info: [db_manager.py:229] 數據庫初始化完成
2025-05-28 11:00:31 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:544] 資料庫初始化成功
2025-05-28 11:00:31 [ERROR] logger - error: [main_window.py:564] 錯誤: 資料庫游標為空
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:576] 請使用以下憑據登入：
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:577] 用戶名: admin
2025-05-28 11:00:31 [INFO] logger - info: [main_window.py:578] 密碼: admin
2025-05-28 11:00:32 [INFO] logger - info: [main_window.py:587] 創建登入對話框...
2025-05-28 11:00:32 [INFO] logger - info: [main_window.py:591] 顯示登入對話框...
2025-05-28 11:00:37 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:37 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:37 [INFO] logger - info: [login_dialog.py:177] 嘗試登入: 用戶名 = admin
2025-05-28 11:00:37 [INFO] logger - info: [login_dialog.py:211] 登入成功: admin
2025-05-28 11:00:37 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:37 [INFO] logger - info: [main_window.py:612] 成功設置歡迎訊息: 歡迎使用蒼藍工程公司業務管理系統 admin
2025-05-28 11:00:40 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:40 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:40 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:42 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:42 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:42 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:43 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:43 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:43 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:43 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:43 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:43 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:50 [INFO] logger - info: [pdf_generator_html.py:14] HTML PDF生成器初始化完成
2025-05-28 11:00:50 [INFO] logger - info: [pdf_manager.py:29] HTML PDF生成器已加載
2025-05-28 11:00:52 [INFO] logger - info: [pdf_generator.py:34] 成功註冊中文字體: 微軟雅黑
2025-05-28 11:00:52 [INFO] logger - info: [pdf_manager.py:37] ReportLab PDF生成器已加載
2025-05-28 11:00:52 [WARNING] logger - warning: [pdf_manager.py:47] FPDF PDF生成器不可用: No module named 'app.utils.pdf_generator_fpdf'
2025-05-28 11:00:52 [INFO] logger - info: [pdf_manager.py:20] PDF管理器初始化完成，默認生成器: html
2025-05-28 11:00:52 [INFO] logger - info: [pdf_generator_html.py:44] 成功生成報價單HTML: D:/AI/HB v.02/output/Quotation_QT-202505-0003.html
2025-05-28 11:00:52 [INFO] logger - info: [pdf_manager.py:112] 使用 html 生成器成功生成 quotation: D:/AI/HB v.02/output/Quotation_QT-202505-0003.html
2025-05-28 11:00:54 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:54 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:54 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 11:00:56 [DEBUG] logger - debug: [db_manager.py:55] 連接到數據庫: database_file/app_data.db
2025-05-28 11:00:56 [INFO] logger - info: [db_manager.py:67] 數據庫連接成功
2025-05-28 11:00:56 [DEBUG] logger - debug: [db_manager.py:106] 數據庫連接已關閉
2025-05-28 12:13:30 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 12:13:31 [INFO] logger - info: [pdf_manager.py:37] ⚠️  WeasyPrint不可用: WeasyPrint is required but not installed. Please run: pip install weasyprint
2025-05-28 12:13:31 [INFO] logger - info: [pdf_manager.py:49] ⚠️  ReportLab不可用: ReportLab is required but not installed. Please run: pip install reportlab
2025-05-28 12:13:31 [INFO] logger - info: [pdf_manager.py:61] ⚠️  傳統ReportLab生成器不可用: No module named 'app.utils.pdf_generator'
2025-05-28 12:13:31 [ERROR] logger - error: [pdf_manager.py:66] ❌ 沒有可用的PDF生成器
2025-05-28 12:13:52 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 12:15:34 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 12:16:01 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 12:16:01 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 12:16:06 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 12:16:06 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 12:16:06 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_SIMPLE-TEST-001.pdf
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:195] 成功生成發票PDF: output/Invoice_Simple_SIMPLE-INV-001.pdf
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:226] 成功生成送貨單PDF: output/DeliveryNote_Simple_SIMPLE-DN-001.pdf
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:37] ⚠️  WeasyPrint不可用: WeasyPrint is required but not installed. Please run: pip install weasyprint
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:46] ✅ 使用簡單ReportLab PDF生成器（推薦選擇）
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:24] PDF管理器初始化完成
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/TEST_ReportLab-Simple_20250528_121607.pdf
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:93] ✅ 成功生成報價單PDF: output/TEST_ReportLab-Simple_20250528_121607.pdf (使用ReportLab-Simple)
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:314] ✅ 測試PDF生成成功: output/TEST_ReportLab-Simple_20250528_121607.pdf (3414 bytes)
2025-05-28 12:16:07 [INFO] logger - info: [pdf_manager.py:319] 🗑️  測試文件已清理
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 12:16:07 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_SIMPLE-LARGE-001.pdf
2025-05-28 14:34:00 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:34:00 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:34:00 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_OPTIMIZED-001.pdf
2025-05-28 14:34:50 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:34:50 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:34:51 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_OPTIMIZED-001.pdf
2025-05-28 14:39:37 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:39:37 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:39:37 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_NO-BORDER-001.pdf
2025-05-28 14:40:06 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:40:06 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:40:06 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_NO-BORDER-001.pdf
2025-05-28 14:43:40 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:43:40 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:43:40 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_TABLE-FORMAT-001.pdf
2025-05-28 14:44:18 [INFO] logger - info: [logger.py:63] 系統日誌初始化完成
2025-05-28 14:44:18 [INFO] logger - info: [pdf_generator_simple.py:64] 簡單PDF生成器初始化完成
2025-05-28 14:44:19 [INFO] logger - info: [pdf_generator_simple.py:164] 成功生成報價單PDF: output/Quotation_Simple_TABLE-FORMAT-001.pdf
