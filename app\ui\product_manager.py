from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView
from app.utils.language_manager import LanguageManager
from app.ui.product_dialog import ProductDialog

class ProductManager(QWidget):
    """產品管理界面"""
    
    def __init__(self, db_manager, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.permission_mgr = permission_mgr
        
        # 初始化界面
        self.init_ui()
        
        # 載入產品數據
        self.load_products()
        
        # 設置權限
        self.setup_permissions()

    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('product_management'))
        layout = QVBoxLayout(self)

        # 按鈕區
        btn_layout = QHBoxLayout()
        self.btn_add = QPushButton(self.lang_manager.get_text('add_product'))
        self.btn_edit = QPushButton(self.lang_manager.get_text('edit_product'))
        self.btn_delete = QPushButton(self.lang_manager.get_text('delete_product'))
        btn_layout.addWidget(self.btn_add)
        btn_layout.addWidget(self.btn_edit)
        btn_layout.addWidget(self.btn_delete)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)

        # 產品表格
        self.table = QTableWidget(0, 6)  # ID, 編號, 名稱, 規格, 單位, 價格
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('product_code'),
            self.lang_manager.get_text('product_name'),
            self.lang_manager.get_text('specification'),
            self.lang_manager.get_text('unit'),
            self.lang_manager.get_text('price')
        ])
        # 隱藏ID列
        self.table.setColumnHidden(0, True)
        # 讓表格列寬自動適應內容
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(self.table.SelectRows)
        layout.addWidget(self.table)

        # 連接按鈕事件
        self.btn_add.clicked.connect(self.add_product)
        self.btn_edit.clicked.connect(self.edit_product)
        self.btn_delete.clicked.connect(self.delete_product)

    def load_products(self):
        # 從數據庫加載產品
        products = self.db_manager.get_all_products()
        self.table.setRowCount(0)
        
        for p in products:
            row = self.table.rowCount()
            self.table.insertRow(row)
            # 顯示所有列，包括ID（但ID列會被隱藏）
            for col, value in enumerate(p):
                self.table.setItem(row, col, QTableWidgetItem(str(value)))

    def add_product(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        dialog = ProductDialog(self)
        if dialog.exec_():
            product_data = dialog.get_data()
            result = self.db_manager.add_product(product_data)
            if result:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                self.load_products()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )

    def edit_product(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
            
        row = selected_rows[0].row()
        product_id = int(self.table.item(row, 0).text())
        product = self.db_manager.get_product(product_id)
        
        if not product:
            QMessageBox.critical(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('operation_failed')
            )
            return
            
        dialog = ProductDialog(self, product)
        if dialog.exec_():
            product_data = dialog.get_data()
            result = self.db_manager.update_product(product_id, product_data)
            if result:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                self.load_products()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )

    def delete_product(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
            
        row = selected_rows[0].row()
        product_id = int(self.table.item(row, 0).text())
        
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('delete_product_confirm'),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            result = self.db_manager.delete_product(product_id)
            if result:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                self.load_products()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
                              
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        self.setWindowTitle(self.lang_manager.get_text('product_management'))
        self.btn_add.setText(self.lang_manager.get_text('add_product'))
        self.btn_edit.setText(self.lang_manager.get_text('edit_product'))
        self.btn_delete.setText(self.lang_manager.get_text('delete_product'))
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('product_code'),
            self.lang_manager.get_text('product_name'),
            self.lang_manager.get_text('specification'),
            self.lang_manager.get_text('unit'),
            self.lang_manager.get_text('price')
        ]) 

    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.btn_add, self.btn_edit, self.btn_delete]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons) 