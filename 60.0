Collecting weasyprint
  Downloading weasyprint-65.1-py3-none-any.whl.metadata (3.7 kB)
Collecting pydyf>=0.11.0 (from weasyprint)
  Downloading pydyf-0.11.0-py3-none-any.whl.metadata (2.5 kB)
Requirement already satisfied: cffi>=0.6 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from weasyprint) (1.17.1)
Collecting tinyhtml5>=2.0.0b1 (from weasyprint)
  Downloading tinyhtml5-2.0.0-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: tinycss2>=1.4.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from weasyprint) (1.4.0)
Collecting cssselect2>=0.8.0 (from weasyprint)
  Downloading cssselect2-0.8.0-py3-none-any.whl.metadata (2.9 kB)
Collecting Pyphen>=0.9.1 (from weasyprint)
  Downloading pyphen-0.17.2-py3-none-any.whl.metadata (3.2 kB)
Requirement already satisfied: Pillow>=9.1.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from weasyprint) (10.2.0)
Requirement already satisfied: fonttools>=4.0.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from fonttools[woff]>=4.0.0->weasyprint) (4.56.0)
Requirement already satisfied: pycparser in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from cffi>=0.6->weasyprint) (2.22)
Requirement already satisfied: webencodings in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from cssselect2>=0.8.0->weasyprint) (0.5.1)
Collecting brotli>=1.0.1 (from fonttools[woff]>=4.0.0->weasyprint)
  Downloading Brotli-1.1.0-cp312-cp312-win_amd64.whl.metadata (5.6 kB)
Collecting zopfli>=0.1.4 (from fonttools[woff]>=4.0.0->weasyprint)
  Downloading zopfli-0.2.3.post1-cp312-cp312-win_amd64.whl.metadata (3.0 kB)
Downloading weasyprint-65.1-py3-none-any.whl (298 kB)
Downloading cssselect2-0.8.0-py3-none-any.whl (15 kB)
Downloading pydyf-0.11.0-py3-none-any.whl (8.1 kB)
Downloading pyphen-0.17.2-py3-none-any.whl (2.1 MB)
   ---------------------------------------- 2.1/2.1 MB 760.3 kB/s eta 0:00:00
Downloading tinyhtml5-2.0.0-py3-none-any.whl (39 kB)
Downloading Brotli-1.1.0-cp312-cp312-win_amd64.whl (357 kB)
Downloading zopfli-0.2.3.post1-cp312-cp312-win_amd64.whl (99 kB)
Installing collected packages: brotli, zopfli, tinyhtml5, Pyphen, pydyf, cssselect2, weasyprint
  Attempting uninstall: cssselect2
    Found existing installation: cssselect2 0.7.0
    Uninstalling cssselect2-0.7.0:
      Successfully uninstalled cssselect2-0.7.0
Successfully installed Pyphen-0.17.2 brotli-1.1.0 cssselect2-0.8.0 pydyf-0.11.0 tinyhtml5-2.0.0 weasyprint-65.1 zopfli-0.2.3.post1
