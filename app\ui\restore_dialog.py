from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QFileDialog, QMessageBox, QGroupBox, QCheckBox,
                             QFrame, QProgressBar, QApplication, QListWidget)
from PyQt5.QtCore import Qt, QCoreApplication
import os
import sqlite3
import json
import datetime
from app.utils.language_manager import LanguageManager

class RestoreDialog(QDialog):
    """還原數據對話框"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.backup_file_path = None
        self.backup_data = None
        
        # 設置對話框屬性
        self.setWindowTitle(self.lang_manager.get_text('restore_data'))
        self.resize(500, 400)
        self.setModal(True)
        
        # 初始化界面
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        
        # 標題
        title_label = QLabel(self.lang_manager.get_text('restore_data'))
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 分隔線
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 說明文字
        description = QLabel(self.lang_manager.get_text('restore_description'))
        description.setWordWrap(True)
        main_layout.addWidget(description)
        
        # 選擇備份文件區域
        file_group = QGroupBox(self.lang_manager.get_text('select_backup_file'))
        file_layout = QHBoxLayout(file_group)
        
        self.file_path_label = QLabel("")
        self.file_path_label.setWordWrap(True)
        self.file_path_label.setStyleSheet("background-color: #f5f5f5; padding: 5px; border: 1px solid #ddd;")
        
        browse_button = QPushButton("...")
        browse_button.setMaximumWidth(40)
        browse_button.clicked.connect(self.select_backup_file)
        
        file_layout.addWidget(self.file_path_label)
        file_layout.addWidget(browse_button)
        main_layout.addWidget(file_group)
        
        # 備份文件信息區域
        self.info_group = QGroupBox(self.lang_manager.get_text('backup_file_info'))
        info_layout = QVBoxLayout(self.info_group)
        
        # 備份日期
        self.date_label = QLabel("")
        info_layout.addWidget(self.date_label)
        
        # 包含的資料表
        self.tables_list = QListWidget()
        info_layout.addWidget(self.tables_list)
        
        # 默認隱藏信息區域，直到選擇了有效的備份文件
        self.info_group.setVisible(False)
        main_layout.addWidget(self.info_group)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 按鈕區域
        buttons_layout = QHBoxLayout()
        self.restore_btn = QPushButton(self.lang_manager.get_text('start_restore'))
        self.restore_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.restore_btn.clicked.connect(self.start_restore)
        self.restore_btn.setEnabled(False)  # 默認禁用，直到選擇了有效的備份文件
        
        self.cancel_btn = QPushButton(self.lang_manager.get_text('cancel'))
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.restore_btn)
        buttons_layout.addWidget(self.cancel_btn)
        main_layout.addLayout(buttons_layout)
    
    def select_backup_file(self):
        """選擇要還原的備份文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.lang_manager.get_text('select_backup_file'),
            "",
            self.lang_manager.get_text('backup_files') + " (*.hbbackup)"
        )
        
        if not file_path:
            return  # 用戶取消了選擇
        
        # 嘗試讀取備份文件
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
                
            # 檢查備份數據格式是否正確
            if not self.validate_backup_format(backup_data):
                raise ValueError(self.lang_manager.get_text('invalid_backup_file'))
            
            # 保存備份文件路徑和數據
            self.backup_file_path = file_path
            self.backup_data = backup_data
            
            # 更新文件路徑顯示
            self.file_path_label.setText(file_path)
            
            # 更新備份信息顯示
            self.update_backup_info()
            
            # 啟用還原按鈕
            self.restore_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('invalid_backup_file')}\n{str(e)}"
            )
            
            # 清除已選擇的文件
            self.backup_file_path = None
            self.backup_data = None
            self.file_path_label.setText("")
            self.info_group.setVisible(False)
            self.restore_btn.setEnabled(False)
    
    def validate_backup_format(self, backup_data):
        """驗證備份數據格式是否正確"""
        # 檢查必要的字段
        if not isinstance(backup_data, dict):
            return False
            
        if 'metadata' not in backup_data or 'data' not in backup_data:
            return False
            
        metadata = backup_data['metadata']
        if 'backup_date' not in metadata or 'tables' not in metadata:
            return False
            
        # 確保數據是字典
        if not isinstance(backup_data['data'], dict):
            return False
            
        # 確保每個表都有schema和data字段
        for table_name, table_data in backup_data['data'].items():
            if 'schema' not in table_data or 'data' not in table_data:
                return False
        
        return True
    
    def update_backup_info(self):
        """更新備份文件信息顯示"""
        if not self.backup_data:
            return
            
        metadata = self.backup_data['metadata']
        
        # 顯示備份日期
        backup_date = datetime.datetime.fromisoformat(metadata['backup_date']).strftime('%Y-%m-%d %H:%M:%S')
        self.date_label.setText(f"{self.lang_manager.get_text('backup_date')}: {backup_date}")
        
        # 顯示包含的資料表
        self.tables_list.clear()
        for table_name in metadata['tables']:
            # 獲取表格的顯示名稱
            display_name = self.get_table_display_name(table_name)
            self.tables_list.addItem(f"{display_name} ({table_name})")
        
        # 顯示信息區域
        self.info_group.setVisible(True)
    
    def get_table_display_name(self, table_name):
        """獲取資料表的顯示名稱"""
        table_key_map = {
            'customers': 'customers_table',
            'products': 'products_table',
            'quotations': 'quotations_table',
            'quotation_items': 'quotation_items_table',
            'invoices': 'invoices_table',
            'invoice_items': 'invoice_items_table',
            'delivery_notes': 'delivery_notes_table',
            'delivery_items': 'delivery_items_table',
            'users': 'users_table'
        }
        
        if table_name in table_key_map:
            return self.lang_manager.get_text(table_key_map[table_name])
        else:
            return table_name
    
    def start_restore(self):
        """開始還原流程"""
        if not self.backup_file_path or not self.backup_data:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('no_backup_file_selected')
            )
            return
        
        # 確認是否執行還原操作
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('restore_confirm'),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 禁用按鈕，避免重複點擊
        self.restore_btn.setEnabled(False)
        self.cancel_btn.setEnabled(False)
        
        try:
            # 執行還原操作
            self.execute_restore()
            
            # 還原完成
            QMessageBox.information(
                self,
                self.lang_manager.get_text('success'),
                self.lang_manager.get_text('restore_completed')
            )
            
            self.accept()  # 關閉對話框
            
        except Exception as e:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('restore_failed')}\n{str(e)}"
            )
            
            # 重新啟用按鈕
            self.restore_btn.setEnabled(True)
            self.cancel_btn.setEnabled(True)
            # 隱藏進度條
            self.progress_bar.setVisible(False)
    
    def execute_restore(self):
        """執行還原操作"""
        try:
            # 建立數據庫連接
            if not self.db_manager.connect():
                raise Exception(self.lang_manager.get_text('database_connection_error'))
            
            # 獲取要還原的表格及數據
            tables_to_restore = self.backup_data['metadata']['tables']
            backup_data = self.backup_data['data']
            total_tables = len(tables_to_restore)
            
            # 啟用事務，確保還原過程的原子性
            self.db_manager.conn.execute("BEGIN TRANSACTION")
            
            # 逐個處理表格
            for i, table_name in enumerate(tables_to_restore):
                # 更新進度條
                self.progress_bar.setValue(int((i / total_tables) * 100))
                QApplication.processEvents()  # 確保UI更新
                
                # 獲取表格數據
                table_data = backup_data.get(table_name)
                if not table_data:
                    continue
                
                # 清空目標表格
                self.db_manager.cursor.execute(f"DELETE FROM {table_name}")
                
                # 對每條記錄進行插入
                table_records = table_data.get('data', [])
                for record in table_records:
                    # 提取列名和值
                    columns = list(record.keys())
                    values = list(record.values())
                    
                    # 構建INSERT語句
                    placeholders = ','.join(['?' for _ in columns])
                    columns_string = ','.join(columns)
                    query = f"INSERT INTO {table_name} ({columns_string}) VALUES ({placeholders})"
                    
                    # 執行插入
                    self.db_manager.cursor.execute(query, values)
            
            # 提交事務
            self.db_manager.conn.commit()
            
            # 關閉數據庫連接
            self.db_manager.disconnect()
            
            # 完成進度條
            self.progress_bar.setValue(100)
            
        except Exception as e:
            # 回滾事務
            if self.db_manager.conn:
                self.db_manager.conn.rollback()
                self.db_manager.disconnect()
            raise e 