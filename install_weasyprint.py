#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import platform
import subprocess
import importlib.util

def check_python_version():
    """檢查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ WeasyPrint需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_package_installed(package_name):
    """檢查Python包是否已安裝"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def run_command(command, description):
    """運行系統命令"""
    print(f"🔄 {description}...")
    print(f"   執行: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description}成功")
            return True
        else:
            print(f"❌ {description}失敗")
            print(f"   錯誤: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description}失敗: {e}")
        return False

def install_system_dependencies():
    """安裝系統依賴"""
    system = platform.system().lower()
    
    print(f"🖥️  檢測到系統: {platform.system()} {platform.release()}")
    
    if system == "windows":
        print("📝 Windows系統通常不需要額外的系統依賴")
        print("   如果安裝失敗，請考慮使用conda或安裝Visual C++ Build Tools")
        return True
        
    elif system == "linux":
        print("🐧 Linux系統需要安裝系統依賴...")
        
        # 檢測Linux發行版
        try:
            with open('/etc/os-release', 'r') as f:
                os_info = f.read().lower()
                
            if 'ubuntu' in os_info or 'debian' in os_info:
                commands = [
                    "sudo apt-get update",
                    "sudo apt-get install -y python3-dev python3-pip python3-cffi python3-brotli",
                    "sudo apt-get install -y libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0",
                    "sudo apt-get install -y libffi-dev shared-mime-info"
                ]
                
                for cmd in commands:
                    if not run_command(cmd, f"安裝依賴: {cmd.split()[-1]}"):
                        print("⚠️  某些依賴安裝失敗，但可能不影響WeasyPrint運行")
                        
            elif 'centos' in os_info or 'rhel' in os_info or 'fedora' in os_info:
                commands = [
                    "sudo yum install -y python3-devel python3-pip python3-cffi",
                    "sudo yum install -y pango harfbuzz pango-devel harfbuzz-devel",
                    "sudo yum install -y libffi-devel shared-mime-info"
                ]
                
                for cmd in commands:
                    if not run_command(cmd, f"安裝依賴: {cmd.split()[-1]}"):
                        print("⚠️  某些依賴安裝失敗，但可能不影響WeasyPrint運行")
                        
            else:
                print("⚠️  未識別的Linux發行版，請手動安裝以下依賴:")
                print("   - pango, harfbuzz, libffi")
                print("   - python3-dev, python3-cffi")
                
        except Exception as e:
            print(f"⚠️  無法檢測Linux發行版: {e}")
            print("   請手動安裝pango, harfbuzz, libffi等依賴")
            
        return True
        
    elif system == "darwin":  # macOS
        print("🍎 macOS系統需要安裝系統依賴...")
        
        # 檢查是否安裝了Homebrew
        if run_command("which brew", "檢查Homebrew"):
            commands = [
                "brew install pango",
                "brew install harfbuzz",
                "brew install libffi"
            ]
            
            for cmd in commands:
                run_command(cmd, f"安裝依賴: {cmd.split()[-1]}")
        else:
            print("⚠️  未檢測到Homebrew，請先安裝Homebrew:")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            print("   然後運行: brew install pango harfbuzz libffi")
            
        return True
        
    else:
        print(f"⚠️  未知系統: {system}")
        print("   請手動安裝pango, harfbuzz, libffi等依賴")
        return True

def install_python_packages():
    """安裝Python包"""
    packages = [
        "cffi>=1.15.0",
        "cairocffi>=1.4.0", 
        "html5lib>=1.1",
        "cssselect2>=0.7.0",
        "weasyprint>=60.0"
    ]
    
    print("📦 安裝Python包...")
    
    for package in packages:
        package_name = package.split('>=')[0].split('==')[0]
        
        if check_package_installed(package_name):
            print(f"✅ {package_name} 已安裝")
            continue
            
        if not run_command(f"pip install {package}", f"安裝 {package}"):
            print(f"⚠️  {package} 安裝失敗，嘗試使用 --user 參數")
            if not run_command(f"pip install --user {package}", f"安裝 {package} (用戶模式)"):
                print(f"❌ {package} 安裝失敗")
                return False
    
    return True

def test_weasyprint():
    """測試WeasyPrint安裝"""
    print("🧪 測試WeasyPrint安裝...")
    
    try:
        import weasyprint
        print(f"✅ WeasyPrint安裝成功，版本: {weasyprint.__version__}")
        
        # 簡單測試
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; }
            </style>
        </head>
        <body>
            <h1>WeasyPrint測試</h1>
            <p>如果您看到這個PDF，說明WeasyPrint安裝成功！</p>
        </body>
        </html>
        """
        
        # 創建測試PDF
        os.makedirs('output', exist_ok=True)
        test_pdf = 'output/weasyprint_test.pdf'
        
        html_doc = weasyprint.HTML(string=html_content)
        html_doc.write_pdf(test_pdf)
        
        if os.path.exists(test_pdf):
            file_size = os.path.getsize(test_pdf)
            print(f"✅ 測試PDF生成成功: {test_pdf} ({file_size} bytes)")
            return True
        else:
            print("❌ 測試PDF生成失敗")
            return False
            
    except ImportError as e:
        print(f"❌ WeasyPrint導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ WeasyPrint測試失敗: {e}")
        return False

def main():
    """主安裝函數"""
    print("🚀 WeasyPrint安裝程序")
    print("=" * 50)
    
    # 檢查Python版本
    if not check_python_version():
        return False
    
    print()
    
    # 安裝系統依賴
    if not install_system_dependencies():
        print("❌ 系統依賴安裝失敗")
        return False
    
    print()
    
    # 安裝Python包
    if not install_python_packages():
        print("❌ Python包安裝失敗")
        return False
    
    print()
    
    # 測試安裝
    if not test_weasyprint():
        print("❌ WeasyPrint測試失敗")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 WeasyPrint安裝完成！")
    print("\n📝 接下來您可以:")
    print("   1. 運行 python test_weasyprint_generator.py 測試PDF生成器")
    print("   2. 在您的應用中使用WeasyPrint生成專業PDF")
    print("\n💡 如果遇到問題:")
    print("   - Windows: 考慮使用conda環境")
    print("   - Linux: 確保安裝了所有系統依賴")
    print("   - macOS: 確保安裝了Homebrew和相關依賴")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        print("\n按任意鍵退出...")
        input()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  安裝被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安裝過程中發生未預期的錯誤: {e}")
        import traceback
        traceback.print_exc()
        print("\n按任意鍵退出...")
        input()
        sys.exit(1) 