from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                             QLabel, QLineEdit, QComboBox, QFrame, QToolBar, QAction,
                             QDateEdit, QFormLayout, QGroupBox, QFileDialog)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager
from app.utils.logger import system_logger
from app.ui.quotation_dialog import QuotationDialog

class QuotationManager(QWidget):
    """報價單管理界面"""
    
    def __init__(self, db_manager, main_window=None, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.main_window = main_window
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.permission_mgr = permission_mgr
        
        # 初始化界面
        self.init_ui()
        
        # 載入報價單數據
        self.load_quotations()
        
        # 設置權限
        self.setup_permissions()
        
    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.btn_add, self.btn_edit, self.btn_delete, 
                        self.btn_to_invoice, self.btn_to_delivery]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            if btn:  # 確保按鈕存在
                btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons)
        
    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('quotation'))
        main_layout = QVBoxLayout(self)
        
        # 添加頁面標題
        title_layout = QHBoxLayout()
        title_label = QLabel(self.lang_manager.get_text('quotation'))
        title_label.setObjectName("page-title")
        title_label.setFont(title_label.font())
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 搜索和過濾區域
        filter_group = QGroupBox(self.lang_manager.get_text('search_and_filter'))
        filter_layout = QFormLayout(filter_group)
        
        # 水平布局用於搜索
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        search_btn = QPushButton(self.lang_manager.get_text('search'))
        search_btn.setIcon(self.icon_manager.get_icon('search'))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        filter_layout.addRow(self.lang_manager.get_text('search_criteria'), search_layout)
        
        # 日期過濾器
        date_layout = QHBoxLayout()
        self.date_from = QDateEdit(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit(QDate.currentDate())
        self.date_from.setCalendarPopup(True)
        self.date_to.setCalendarPopup(True)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('from')))
        date_layout.addWidget(self.date_from)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('to')))
        date_layout.addWidget(self.date_to)
        filter_layout.addRow(self.lang_manager.get_text('date_range'), date_layout)
        
        # 狀態過濾器
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.lang_manager.get_text('all_status'))
        self.status_filter.addItem(self.lang_manager.get_text('draft'))
        self.status_filter.addItem(self.lang_manager.get_text('sent'))
        self.status_filter.addItem(self.lang_manager.get_text('approved'))
        self.status_filter.addItem(self.lang_manager.get_text('rejected'))
        filter_layout.addRow(self.lang_manager.get_text('status'), self.status_filter)
        
        main_layout.addWidget(filter_group)
        
        # 按鈕工具列 - 使用QHBoxLayout代替QToolBar，以實現更好的佈局控制
        buttons_layout = QHBoxLayout()
        
        # 創建分組容器以美化佈局
        basic_actions = QGroupBox("")
        basic_actions.setFlat(True)
        basic_layout = QHBoxLayout(basic_actions)
        basic_layout.setContentsMargins(0, 0, 0, 0)
        basic_layout.setSpacing(2)
        
        # 基本操作按鈕
        self.btn_add = QPushButton(self.lang_manager.get_text('add_quotation'))
        self.btn_add.setIcon(self.icon_manager.get_icon('add'))
        self.btn_add.setMinimumWidth(120)
        
        self.btn_edit = QPushButton(self.lang_manager.get_text('edit_quotation'))
        self.btn_edit.setIcon(self.icon_manager.get_icon('edit'))
        self.btn_edit.setMinimumWidth(120)
        
        self.btn_delete = QPushButton(self.lang_manager.get_text('delete_quotation'))
        self.btn_delete.setIcon(self.icon_manager.get_icon('delete'))
        self.btn_delete.setMinimumWidth(120)
        
        self.btn_view = QPushButton(self.lang_manager.get_text('view_quotation'))
        self.btn_view.setIcon(self.icon_manager.get_icon('view'))
        self.btn_view.setMinimumWidth(120)
        
        basic_layout.addWidget(self.btn_add)
        basic_layout.addWidget(self.btn_edit)
        basic_layout.addWidget(self.btn_delete)
        basic_layout.addWidget(self.btn_view)
        buttons_layout.addWidget(basic_actions)
        
        # 垂直分隔線
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line)
        
        # 轉換操作按鈕組
        convert_actions = QGroupBox("")
        convert_actions.setFlat(True)
        convert_layout = QHBoxLayout(convert_actions)
        convert_layout.setContentsMargins(0, 0, 0, 0)
        convert_layout.setSpacing(2)
        
        self.btn_to_invoice = QPushButton(self.lang_manager.get_text('to_invoice'))
        self.btn_to_invoice.setIcon(self.icon_manager.get_icon('invoice'))
        self.btn_to_invoice.setMinimumWidth(120)
        
        self.btn_to_delivery = QPushButton(self.lang_manager.get_text('to_delivery_note'))
        self.btn_to_delivery.setIcon(self.icon_manager.get_icon('delivery'))
        self.btn_to_delivery.setMinimumWidth(120)
        
        convert_layout.addWidget(self.btn_to_invoice)
        convert_layout.addWidget(self.btn_to_delivery)
        buttons_layout.addWidget(convert_actions)
        
        # 垂直分隔線
        separator_line2 = QFrame()
        separator_line2.setFrameShape(QFrame.VLine)
        separator_line2.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line2)
        
        # 輸出操作按鈕組
        output_actions = QGroupBox("")
        output_actions.setFlat(True)
        output_layout = QHBoxLayout(output_actions)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(2)
        
        self.btn_print = QPushButton(self.lang_manager.get_text('print'))
        self.btn_print.setIcon(self.icon_manager.get_icon('print'))
        self.btn_print.setMinimumWidth(80)
        
        self.btn_export = QPushButton(self.lang_manager.get_text('export'))
        self.btn_export.setIcon(self.icon_manager.get_icon('export'))
        self.btn_export.setMinimumWidth(80)
        
        output_layout.addWidget(self.btn_print)
        output_layout.addWidget(self.btn_export)
        buttons_layout.addWidget(output_actions)
        
        # 添加彈性空間，使按鈕靠左對齊
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # 添加一條分隔線
        bottom_separator = QFrame()
        bottom_separator.setFrameShape(QFrame.HLine)
        bottom_separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(bottom_separator)
        
        # 報價單表格
        self.table = QTableWidget(0, 7)  # ID, 報價單號, 客戶, 日期, 有效期, 總金額, 狀態
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('quotation_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('date'),
            self.lang_manager.get_text('valid_until'),
            self.lang_manager.get_text('total_amount'),
            self.lang_manager.get_text('status')
        ])
        
        # 隱藏ID列
        self.table.setColumnHidden(0, True)
        
        # 表格佈局
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(self.table.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        main_layout.addWidget(self.table)
        
        # 狀態標籤
        self.status_label = QLabel(f"{self.lang_manager.get_text('total_records')}: 0")
        main_layout.addWidget(self.status_label)
        
        # 連接按鈕事件
        self.btn_add.clicked.connect(self.add_quotation)
        self.btn_edit.clicked.connect(self.edit_quotation)
        self.btn_delete.clicked.connect(self.delete_quotation)
        self.btn_view.clicked.connect(self.view_quotation)
        self.btn_to_invoice.clicked.connect(self.convert_to_invoice)
        self.btn_to_delivery.clicked.connect(self.convert_to_delivery)
        self.btn_print.clicked.connect(self.print_quotation)
        self.btn_export.clicked.connect(self.export_quotation)
        
        search_btn.clicked.connect(self.search_quotation)
        self.status_filter.currentIndexChanged.connect(self.filter_by_status)
        
    def add_quotation(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        dialog = QuotationDialog(self, self.db_manager)
        if dialog.exec_():
            # 獲取數據
            quotation_data = dialog.get_data()
            
            # 保存到數據庫
            success = self.db_manager.add_quotation(quotation_data)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                self.load_quotations()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
                
    def edit_quotation(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_items = self.table.selectedItems()
        if not selected_items:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        row = selected_items[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取報價單數據
        quotation_data = self.db_manager.get_quotation(quotation_id)
        
        if not quotation_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('quotation_not_found')
            )
            return
        
        # 打開編輯對話框
        dialog = QuotationDialog(self, self.db_manager, quotation_data)
        if dialog.exec_():
            quotation_data = dialog.get_data()
            success = self.db_manager.update_quotation(quotation_id, quotation_data)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                self.load_quotations()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
                
    def delete_quotation(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_items = self.table.selectedItems()
        if not selected_items:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        row = selected_items[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        quotation_no = self.table.item(row, 1).text()
        
        # 確認刪除
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('delete_confirm').format(quotation_no=quotation_no),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 獲取當前用戶信息
            deleted_by = "system"
            if self.main_window and hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                deleted_by = self.main_window.current_user.get('username', 'system')
            
            success = self.db_manager.delete_quotation(quotation_id, deleted_by)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    "報價單已移動到回收站"
                )
                self.load_quotations()
            else:
                QMessageBox.critical(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
                
    def view_quotation(self):
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取報價單ID
        row = selected_rows[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取報價單數據
        quotation_data = self.db_manager.get_quotation(quotation_id)
        
        if not quotation_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('quotation_not_found')
            )
            return
        
        # 打開報價單對話框（只讀模式）
        dialog = QuotationDialog(self, self.db_manager, quotation_data)
        # 禁用所有輸入控件
        dialog.setReadOnly(True)
        dialog.btn_save.setVisible(False)
        dialog.btn_preview.setText(self.lang_manager.get_text('print'))
        dialog.btn_cancel.setText(self.lang_manager.get_text('close'))
        dialog.exec_()
    
    def convert_to_invoice(self):
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取報價單ID
        row = selected_rows[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        quotation_no = self.table.item(row, 1).text()
        status = self.table.item(row, 6).text()
        
        # 檢查狀態，防止重複轉換
        if status == '已轉換':
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                f"此報價單 ({quotation_no}) 已被轉換，不能重複轉換。"
            )
            return
        
        # 確認轉換
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('convert_to_invoice_confirm').format(quotation_no=quotation_no),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 從數據庫獲取報價單數據
            quotation_data = self.db_manager.get_quotation(quotation_id)
            
            if not quotation_data:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('quotation_not_found')
                )
                return
            
            # 轉換為發票數據
            invoice_data = self.db_manager.convert_quotation_to_invoice(quotation_id)
            
            if invoice_data:
                # 發票創建成功，顯示來源資訊
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    f"{self.lang_manager.get_text('invoice_created')}\n\n" +
                    f"從報價單 {quotation_no} 轉換\n" +
                    f"發票編號: {invoice_data['invoice_no']}"
                )
                
                # 刷新報價單狀態
                self.load_quotations()
                
                # 選擇最可靠的方式獲取主窗口
                if self.main_window and hasattr(self.main_window, 'show_invoice_manager'):
                    # 方法1: 使用傳入的主窗口引用 (最可靠)
                    self.main_window.show_invoice_manager()
                    return
                
                # 方法2: 使用全局變量
                try:
                    from app.main import global_main_window
                    if global_main_window and hasattr(global_main_window, 'show_invoice_manager'):
                        global_main_window.show_invoice_manager()
                        return
                except (ImportError, AttributeError):
                    # 全局變量不可用，繼續嘗試其他方法
                    system_logger.debug("無法通過全局變量獲取主窗口引用")
                
                # 方法3: 找到父窗口
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if widget.__class__.__name__ == 'MainWindow' and hasattr(widget, 'show_invoice_manager'):
                        widget.show_invoice_manager()
                        return
                
                # 如果所有方法都失敗，顯示錯誤並刷新當前報價單列表
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('error'),
                    "無法切換到發票管理頁面，但發票已成功創建。請手動切換到發票頁面查看。"
                )
                self.load_quotations()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def convert_to_delivery(self):
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取報價單ID
        row = selected_rows[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        quotation_no = self.table.item(row, 1).text()
        status = self.table.item(row, 6).text()
        
        # 檢查狀態，防止重複轉換
        if status == '已轉換':
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                f"此報價單 ({quotation_no}) 已被轉換，不能重複轉換。"
            )
            return
        
        # 確認轉換
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('convert_to_delivery_confirm').format(quotation_no=quotation_no),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 從數據庫獲取報價單數據
            quotation_data = self.db_manager.get_quotation(quotation_id)
            
            if not quotation_data:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('quotation_not_found')
                )
                return
            
            # 轉換為送貨單數據
            delivery_data = self.db_manager.convert_quotation_to_delivery(quotation_id)
            
            if delivery_data:
                # 送貨單創建成功，顯示來源資訊
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    f"{self.lang_manager.get_text('delivery_note_created')}\n\n" +
                    f"從報價單 {quotation_no} 轉換\n" +
                    f"送貨單編號: {delivery_data['delivery_no']}"
                )
                
                # 刷新報價單狀態
                self.load_quotations()
                
                # 選擇最可靠的方式獲取主窗口
                if self.main_window and hasattr(self.main_window, 'show_delivery_note_manager'):
                    # 方法1: 使用傳入的主窗口引用 (最可靠)
                    self.main_window.show_delivery_note_manager()
                    return
                
                # 方法2: 使用全局變量
                try:
                    from app.main import global_main_window
                    if global_main_window and hasattr(global_main_window, 'show_delivery_note_manager'):
                        global_main_window.show_delivery_note_manager()
                        return
                except (ImportError, AttributeError):
                    # 全局變量不可用，繼續嘗試其他方法
                    system_logger.debug("無法通過全局變量獲取主窗口引用")
                
                # 方法3: 找到父窗口
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if widget.__class__.__name__ == 'MainWindow' and hasattr(widget, 'show_delivery_note_manager'):
                        widget.show_delivery_note_manager()
                        return
                
                # 如果所有方法都失敗，顯示錯誤並刷新當前報價單列表
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('error'),
                    "無法切換到送貨單管理頁面，但送貨單已成功創建。請手動切換到送貨單頁面查看。"
                )
                self.load_quotations()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def print_quotation(self):
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取報價單ID
        row = selected_rows[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取報價單數據
        quotation_data = self.db_manager.get_quotation(quotation_id)
        
        if not quotation_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('quotation_not_found')
            )
            return
        
        # 調用PDF生成功能
        try:
            # 使用統一的PDF管理器
            from app.utils.pdf_manager import PDFManager
            pdf_manager = PDFManager()
            pdf_file = pdf_manager.generate_quotation_pdf(quotation_data)
            
            if pdf_file:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('pdf_generated').format(file_path=pdf_file)
                )
                
                # 嘗試打開PDF檔案
                import os
                os.startfile(pdf_file)
            else:
                raise Exception("PDF generation failed")
        except ImportError:
            # PDF生成器尚未實現
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('tip'),
                self.lang_manager.get_text('pdf_function_not_implemented')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('pdf_generation_error')}: {str(e)}"
            )
    
    def export_quotation(self):
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取報價單ID
        row = selected_rows[0].row()
        quotation_id = int(self.table.item(row, 0).text())
        quotation_no = self.table.item(row, 1).text()
        
        # 從數據庫獲取報價單數據
        quotation_data = self.db_manager.get_quotation(quotation_id)
        
        if not quotation_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('quotation_not_found')
            )
            return
        
        # 讓用戶選擇匯出格式（Excel或CSV）
        file_path, file_type = QFileDialog.getSaveFileName(
            self,
            self.lang_manager.get_text('export_quotation'),
            f"{quotation_no}.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if not file_path:
            return  # 用戶取消了
        
        try:
            # 根據文件類型決定匯出格式
            if file_path.endswith('.xlsx'):
                # 匯出為Excel
                import pandas as pd
                
                # 準備數據
                # 主表數據
                main_data = {
                    self.lang_manager.get_text('quotation_no'): [quotation_data['quotation_no']],
                    self.lang_manager.get_text('date'): [quotation_data['date']],
                    self.lang_manager.get_text('valid_until'): [quotation_data['valid_until']],
                    self.lang_manager.get_text('customer'): [quotation_data['customer_name']],
                    self.lang_manager.get_text('contact_person'): [quotation_data['contact_person']],
                    self.lang_manager.get_text('phone'): [quotation_data['phone']],
                    self.lang_manager.get_text('status'): [quotation_data['status']],
                    self.lang_manager.get_text('total_amount'): [quotation_data['total_amount']]
                }
                
                # 項目數據
                items_data = []
                for item in quotation_data['items']:
                    items_data.append({
                        self.lang_manager.get_text('item_no'): item['item_no'],
                        self.lang_manager.get_text('description'): item['description'],
                        self.lang_manager.get_text('quantity'): item['quantity'],
                        self.lang_manager.get_text('unit'): item['unit'],
                        self.lang_manager.get_text('unit_price'): item['unit_price'],
                        self.lang_manager.get_text('discount'): f"{item['discount']}%",
                        self.lang_manager.get_text('amount'): item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                    })
                
                # 創建Excel檔案
                with pd.ExcelWriter(file_path) as writer:
                    pd.DataFrame(main_data).to_excel(writer, sheet_name=self.lang_manager.get_text('quotation_info'), index=False)
                    pd.DataFrame(items_data).to_excel(writer, sheet_name=self.lang_manager.get_text('quotation_items'), index=False)
                
            elif file_path.endswith('.csv'):
                # 匯出為CSV
                import csv
                
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # 寫入標題行
                    writer.writerow([
                        self.lang_manager.get_text('quotation_no'),
                        self.lang_manager.get_text('date'),
                        self.lang_manager.get_text('customer'),
                        self.lang_manager.get_text('item_no'),
                        self.lang_manager.get_text('description'),
                        self.lang_manager.get_text('quantity'),
                        self.lang_manager.get_text('unit'),
                        self.lang_manager.get_text('unit_price'),
                        self.lang_manager.get_text('discount'),
                        self.lang_manager.get_text('amount')
                    ])
                    
                    # 寫入數據行
                    for item in quotation_data['items']:
                        amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                        writer.writerow([
                            quotation_data['quotation_no'],
                            quotation_data['date'],
                            quotation_data['customer_name'],
                            item['item_no'],
                            item['description'],
                            item['quantity'],
                            item['unit'],
                            item['unit_price'],
                            f"{item['discount']}%",
                            f"{amount:.2f}"
                        ])
            
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('success'),
                self.lang_manager.get_text('export_success')
            )
            
            # 嘗試打開文件
            import os
            os.startfile(file_path)
            
        except ImportError:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('export_module_not_installed')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('export_error')}: {str(e)}"
            )
    
    def search_quotation(self):
        """根據搜索條件過濾報價單"""
        search_text = self.search_input.text().strip()
        date_from = self.date_from.date().toString("yyyy-MM-dd")
        date_to = self.date_to.date().toString("yyyy-MM-dd")
        status = self.status_filter.currentText()
        
        # 如果是"所有狀態"，則將status設為None
        if status == self.lang_manager.get_text('all_status'):
            status = None
        
        # 從數據庫加載過濾後的報價單
        self.load_quotations(search_text, date_from, date_to, status)

    def filter_by_status(self):
        """根據狀態過濾報價單"""
        # 重用搜索功能，但只更改狀態過濾
        self.search_quotation()

    def load_quotations(self, search_text="", date_from=None, date_to=None, status=None):
        """從數據庫加載報價單數據"""
        if self.db_manager:
            # 獲取報價單列表
            quotations = self.db_manager.get_quotations(search_text, date_from, date_to, status)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            for quotation in quotations:
                row = self.table.rowCount()
                self.table.insertRow(row)
                
                # 假設quotation是一個字典或元組，包含報價單資料
                # 如果是元組，需要根據數據庫查詢的列順序處理
                # 這裡假設quotation是一個字典
                
                # ID列
                self.table.setItem(row, 0, QTableWidgetItem(str(quotation.get('id', ''))))
                
                # 報價單號
                self.table.setItem(row, 1, QTableWidgetItem(quotation.get('quotation_no', '')))
                
                # 客戶
                self.table.setItem(row, 2, QTableWidgetItem(quotation.get('customer_name', '')))
                
                # 日期
                self.table.setItem(row, 3, QTableWidgetItem(quotation.get('date', '')))
                
                # 有效期至
                self.table.setItem(row, 4, QTableWidgetItem(quotation.get('valid_until', '')))
                
                # 總金額
                total_amount = quotation.get('total_amount', 0)
                total_amount_text = f"{total_amount:,.2f}" if isinstance(total_amount, (int, float)) else total_amount
                self.table.setItem(row, 5, QTableWidgetItem(total_amount_text))
                
                # 狀態
                self.table.setItem(row, 6, QTableWidgetItem(quotation.get('status', '')))
            
            # 更新狀態標籤
            self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}")
        # 刪除或註釋掉這部分，防止加載測試數據
        # else:
        #     # 在測試環境中，加載測試數據
        #     self.add_test_data()

    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()
        self.setWindowTitle(self.lang_manager.get_text('quotation'))
        
        # 更新搜索區域
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        
        # 更新按鈕文字
        self.btn_add.setText(self.lang_manager.get_text('add_quotation'))
        self.btn_edit.setText(self.lang_manager.get_text('edit_quotation'))
        self.btn_delete.setText(self.lang_manager.get_text('delete_quotation'))
        self.btn_view.setText(self.lang_manager.get_text('view_quotation'))
        self.btn_to_invoice.setText(self.lang_manager.get_text('to_invoice'))
        self.btn_to_delivery.setText(self.lang_manager.get_text('to_delivery_note'))
        self.btn_print.setText(self.lang_manager.get_text('print'))
        self.btn_export.setText(self.lang_manager.get_text('export'))
        
        # 更新表格標題
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('quotation_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('date'),
            self.lang_manager.get_text('valid_until'),
            self.lang_manager.get_text('total_amount'),
            self.lang_manager.get_text('status')
        ])
        
        # 更新狀態標籤
        self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}") 