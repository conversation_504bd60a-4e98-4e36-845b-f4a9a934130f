from PyQt5.QtWidgets import QWidget, QFormLayout, QLineEdit, QPushButton, QComboBox, QLabel, QFileDialog, QMessageBox
from configparser import ConfigParser
from app.utils.language_manager import LanguageManager
import os

class SystemSettings(QWidget):
    def __init__(self, config_path='config/settings.ini', permission_mgr=None):
        super().__init__()
        self.config_path = config_path
        self.config = ConfigParser()
        self.config.read(self.config_path, encoding='utf-8')
        self.lang_manager = LanguageManager()
        self.permission_mgr = permission_mgr
        self.init_ui()
        self.load_settings()
        
        # 設置權限
        self.setup_permissions()

    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 系統設置只有管理員可以修改
        if not self.permission_mgr.check_user_management_permission():
            # 如果不是管理員，禁用所有輸入控件和保存按鈕
            self.setReadOnly(True)
            
    def setReadOnly(self, readonly=True):
        """設置界面為只讀模式"""
        # 禁用所有輸入控件
        self.name_cn.setReadOnly(readonly)
        self.name_en.setReadOnly(readonly)
        self.address_cn.setReadOnly(readonly)
        self.address_en.setReadOnly(readonly)
        self.phone.setReadOnly(readonly)
        self.email.setReadOnly(readonly)
        self.logo_path.setReadOnly(readonly)
        self.logo_btn.setEnabled(not readonly)
        
        self.quotation_prefix.setReadOnly(readonly)
        self.invoice_prefix.setReadOnly(readonly)
        self.delivery_note_prefix.setReadOnly(readonly)
        
        self.language.setEnabled(not readonly)
        self.save_btn.setEnabled(not readonly)
            
    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('system_settings'))
        self.layout = QFormLayout(self)

        # 公司資訊
        self.name_cn = QLineEdit()
        self.name_en = QLineEdit()
        self.address_cn = QLineEdit()
        self.address_en = QLineEdit()
        self.phone = QLineEdit()
        self.email = QLineEdit()
        self.logo_path = QLineEdit()
        self.logo_btn = QPushButton(self.lang_manager.get_text('select_logo'))
        self.logo_btn.clicked.connect(self.select_logo)

        self.layout.addRow(self.lang_manager.get_text('company_name_cn'), self.name_cn)
        self.layout.addRow(self.lang_manager.get_text('company_name_en'), self.name_en)
        self.layout.addRow(self.lang_manager.get_text('address_cn'), self.address_cn)
        self.layout.addRow(self.lang_manager.get_text('address_en'), self.address_en)
        self.layout.addRow(self.lang_manager.get_text('phone'), self.phone)
        self.layout.addRow(self.lang_manager.get_text('email'), self.email)
        logo_layout = QWidget()
        logo_form = QFormLayout(logo_layout)
        logo_form.addRow(self.logo_path, self.logo_btn)
        self.layout.addRow(self.lang_manager.get_text('company_logo'), logo_layout)

        # 單據編號規則
        self.quotation_prefix = QLineEdit()
        self.invoice_prefix = QLineEdit()
        self.delivery_note_prefix = QLineEdit()
        self.layout.addRow(self.lang_manager.get_text('quotation_prefix'), self.quotation_prefix)
        self.layout.addRow(self.lang_manager.get_text('invoice_prefix'), self.invoice_prefix)
        self.layout.addRow(self.lang_manager.get_text('delivery_note_prefix'), self.delivery_note_prefix)

        # 預設語言
        self.language = QComboBox()
        self.language.addItems([
            self.lang_manager.get_text('zh_hk'),
            self.lang_manager.get_text('english')
        ])
        self.layout.addRow(self.lang_manager.get_text('default_language'), self.language)

        # 儲存按鈕
        self.save_btn = QPushButton(self.lang_manager.get_text('save_settings'))
        self.save_btn.clicked.connect(self.save_settings)
        self.layout.addRow(self.save_btn)

    def load_settings(self):
        c = self.config['CompanyInfo']
        n = self.config['Numbering']
        a = self.config['Appearance']
        self.name_cn.setText(c.get('name_cn', ''))
        self.name_en.setText(c.get('name_en', ''))
        self.address_cn.setText(c.get('address_cn', ''))
        self.address_en.setText(c.get('address_en', ''))
        self.phone.setText(c.get('phone', ''))
        self.email.setText(c.get('email', ''))
        self.logo_path.setText(c.get('logo_path', ''))
        self.quotation_prefix.setText(n.get('quotation_prefix', 'QT'))
        self.invoice_prefix.setText(n.get('invoice_prefix', 'INV'))
        self.delivery_note_prefix.setText(n.get('delivery_note_prefix', 'DN'))
        lang = a.get('default_language', 'zh_HK')
        self.language.setCurrentIndex(0 if lang == 'zh_HK' else 1)

    def save_settings(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_user_management_permission():
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('no_permission')
            )
            return
            
        self.config['CompanyInfo']['name_cn'] = self.name_cn.text()
        self.config['CompanyInfo']['name_en'] = self.name_en.text()
        self.config['CompanyInfo']['address_cn'] = self.address_cn.text()
        self.config['CompanyInfo']['address_en'] = self.address_en.text()
        self.config['CompanyInfo']['phone'] = self.phone.text()
        self.config['CompanyInfo']['email'] = self.email.text()
        self.config['CompanyInfo']['logo_path'] = self.logo_path.text()
        self.config['Numbering']['quotation_prefix'] = self.quotation_prefix.text()
        self.config['Numbering']['invoice_prefix'] = self.invoice_prefix.text()
        self.config['Numbering']['delivery_note_prefix'] = self.delivery_note_prefix.text()
        self.config['Appearance']['default_language'] = 'zh_HK' if self.language.currentIndex() == 0 else 'en'
        with open(self.config_path, 'w', encoding='utf-8') as f:
            self.config.write(f)
        QMessageBox.information(self, self.lang_manager.get_text('tip'), self.lang_manager.get_text('settings_saved'))

    def select_logo(self):
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_user_management_permission():
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('no_permission')
            )
            return
            
        file, _ = QFileDialog.getOpenFileName(self, self.lang_manager.get_text('select_logo'), '', 'Images (*.png *.jpg *.jpeg *.bmp)')
        if file:
            self.logo_path.setText(file)
            
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        self.setWindowTitle(self.lang_manager.get_text('system_settings'))
        self.logo_btn.setText(self.lang_manager.get_text('select_logo'))
        
        # 刷新表單標籤
        for i in range(self.layout.rowCount()):
            label_item = self.layout.itemAt(i, QFormLayout.LabelRole)
            if label_item and label_item.widget():
                label = label_item.widget()
                if isinstance(label, QLabel):
                    text = label.text()
                    # 根據標籤文字查找對應的翻譯鍵
                    if '公司名稱(中文)' in text or 'Company Name (Chinese)' in text:
                        label.setText(self.lang_manager.get_text('company_name_cn'))
                    elif '公司名稱(英文)' in text or 'Company Name (English)' in text:
                        label.setText(self.lang_manager.get_text('company_name_en'))
                    elif '地址(中文)' in text or 'Address (Chinese)' in text:
                        label.setText(self.lang_manager.get_text('address_cn'))
                    elif '地址(英文)' in text or 'Address (English)' in text:
                        label.setText(self.lang_manager.get_text('address_en'))
                    elif '電話' in text or 'Phone' in text:
                        label.setText(self.lang_manager.get_text('phone'))
                    elif '電郵' in text or 'Email' in text:
                        label.setText(self.lang_manager.get_text('email'))
                    elif '公司Logo' in text or 'Company Logo' in text:
                        label.setText(self.lang_manager.get_text('company_logo'))
                    elif '報價單前綴' in text or 'Quotation Prefix' in text:
                        label.setText(self.lang_manager.get_text('quotation_prefix'))
                    elif '發票前綴' in text or 'Invoice Prefix' in text:
                        label.setText(self.lang_manager.get_text('invoice_prefix'))
                    elif '送貨單前綴' in text or 'Delivery Note Prefix' in text:
                        label.setText(self.lang_manager.get_text('delivery_note_prefix'))
                    elif '預設語言' in text or 'Default Language' in text:
                        label.setText(self.lang_manager.get_text('default_language'))
        
        # 更新下拉選單和按鈕
        self.save_btn.setText(self.lang_manager.get_text('save_settings')) 