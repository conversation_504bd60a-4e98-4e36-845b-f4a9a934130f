# 蒼藍工程公司業務管理系統

這是一個為蒼藍工程公司開發的業務管理系統，用於管理報價單、發票和送貨單等業務文檔。

## 功能特點

- 報價單管理
- 發票管理
- 送貨單管理
- 客戶管理
- 產品管理
- 用戶管理與權限控制
- 系統設定
- 中英文界面切換
- PDF 文件生成
- 數據備份與還原
- 完整的日誌系統
- 統一的錯誤處理

## 系統要求

- Python 3.8 或更高版本
- Windows 10 或更高版本
- 至少 4GB RAM
- 100MB 可用磁盤空間

## 安裝步驟

1. 克隆或下載此專案
2. 創建虛擬環境：
   ```bash
   python -m venv .venv
   ```
3. 激活虛擬環境：
   ```bash
   # Windows
   .venv\Scripts\activate
   
   # Linux/Mac
   source .venv/bin/activate
   ```
4. 安裝依賴：
   ```bash
   pip install -r requirements.txt
   ```

## 運行應用程式

### 方法一：使用批處理文件（推薦）
```bash
start_app.bat
```

### 方法二：使用PowerShell腳本
```powershell
.\start_app.ps1
```

### 方法三：直接使用Python命令
```bash
C:/msys64/mingw64/bin/python.exe app/main.py
```

### 方法四：如果您有其他Python環境
```bash
python app/main.py
```

**注意**：本系統需要PyQt5和ReportLab依賴。如果使用msys2環境，請確保已安裝：
- `mingw-w64-x86_64-python-pyqt5`
- `mingw-w64-x86_64-python-reportlab`

## 首次使用

1. 首次運行時，系統會自動創建必要的目錄和數據庫
2. 默認管理員帳戶：
   - 用戶名：`admin`
   - 密碼：`admin`
3. 登入後請立即修改默認密碼

## 開發環境設置

1. 安裝 Python 3.8 或更高版本
2. 安裝 PyQt5 和相關工具
3. 安裝其他依賴庫
4. 配置開發工具（推薦 VS Code 或 PyCharm）

## 目錄結構

```
HB_Engineering_App/
├── app/                      # 應用程式核心代碼
│   ├── ui/                   # 用戶界面模塊
│   ├── core/                 # 業務邏輯模塊
│   ├── database/            # 數據庫操作模塊
│   ├── utils/               # 工具函數模塊
│   │   ├── logger.py        # 日誌系統
│   │   ├── error_handler.py # 錯誤處理
│   │   ├── resource_manager.py # 資源管理
│   │   └── language_manager.py # 語言管理
│   └── assets/              # 資源文件
│       ├── icons/           # 圖標文件
│       └── images/          # 圖片文件
├── config/                  # 配置文件
│   └── settings.ini         # 主配置文件
├── database_file/          # 數據庫文件
├── output/                 # 輸出文件（PDF等）
├── logs/                   # 日誌文件
├── requirements.txt        # 依賴庫列表
└── run.py                  # 主啟動腳本
```

## 配置說明

配置文件位於 `config/settings.ini`，包含以下主要設置：

### 數據庫配置
```ini
[Database]
type = sqlite
path = database_file/app_data.db
```

### 公司信息
```ini
[CompanyInfo]
name_cn = 蒼藍工程公司
name_en = H.B ENGINEERING COMPANY
address_cn = 景松樓 景林邨 寶林將軍澳
address_en = No. 40, GF Yeung Uk San Chuen,Tung Chau Yuen Long, N.T.
# ... 其他公司信息
```

### 單據編號規則
```ini
[Numbering]
quotation_prefix = QT
invoice_prefix = INV
delivery_note_prefix = DN
```

### 界面設置
```ini
[Appearance]
default_language = zh_HK
```

## 功能模塊

### 1. 用戶管理
- 用戶登入/登出
- 用戶權限管理
- 密碼修改

### 2. 客戶管理
- 客戶資料增刪改查
- 客戶信息搜索

### 3. 產品管理
- 產品資料維護
- 產品規格管理

### 4. 報價單管理
- 創建/編輯報價單
- 報價單轉換為發票/送貨單
- PDF 生成和打印

### 5. 發票管理
- 發票創建和編輯
- 付款狀態追蹤
- 發票轉換為送貨單

### 6. 送貨單管理
- 送貨單創建和管理
- 送貨狀態追蹤

### 7. 系統管理
- 系統設定
- 數據備份與還原
- 日誌查看

## 日誌系統

系統提供完整的日誌記錄功能：

- **位置**：`logs/` 目錄
- **格式**：`hb_system_YYYYMMDD.log`
- **級別**：DEBUG, INFO, WARNING, ERROR, CRITICAL
- **自動輪換**：單個文件最大 5MB，保留 10 個備份
- **自動清理**：保留 30 天的日誌文件

## 錯誤處理

系統採用統一的錯誤處理機制：

- 所有錯誤都會記錄到日誌文件
- 用戶界面顯示友好的錯誤信息
- 數據庫操作具有重試機制
- 資源自動清理和釋放

## 安全特性

- 密碼使用 SHA-256 加密存儲
- 數據庫外鍵約束
- 文件路徑驗證防止路徑遍歷攻擊
- 用戶權限控制

## 性能優化

- 數據庫連接池管理
- 資源懶加載
- 圖標緩存機制
- 日誌文件自動清理

## 故障排除

### 常見問題

1. **無法啟動應用程式**
   - 檢查 Python 版本是否符合要求
   - 確認所有依賴已正確安裝
   - 查看日誌文件獲取詳細錯誤信息

2. **數據庫連接失敗**
   - 檢查 `database_file` 目錄權限
   - 確認數據庫文件未被其他程序佔用

3. **PDF 生成失敗**
   - 檢查 `output` 目錄權限
   - 確認 ReportLab 庫已正確安裝

### 日誌查看

查看最新的日誌文件以獲取詳細的錯誤信息：
```bash
# Windows
type logs\hb_system_YYYYMMDD.log

# Linux/Mac
cat logs/hb_system_YYYYMMDD.log
```

## 開發指南

### 代碼規範

- 使用 Python PEP 8 編碼規範
- 所有函數和類都要有文檔字符串
- 使用類型提示（Type Hints）
- 錯誤處理使用統一的 ErrorHandler 類

### 添加新功能

1. 在相應的模塊中添加業務邏輯
2. 更新數據庫模式（如需要）
3. 添加用戶界面
4. 更新語言文件
5. 添加單元測試
6. 更新文檔

### 測試

```bash
# 運行單元測試
python -m pytest tests/

# 運行特定測試
python -m pytest tests/test_database.py
```

## 部署

### 打包為可執行文件

使用 PyInstaller 打包：

```bash
pip install pyinstaller
pyinstaller --onefile --windowed run.py
```

### 系統要求檢查

部署前請確認目標系統：
- Windows 10 或更高版本
- 足夠的磁盤空間
- 適當的文件系統權限

## 更新日誌

### v1.1.0 (2024-01-XX)
- 添加統一錯誤處理系統
- 改進日誌記錄機制
- 優化資源管理
- 增強安全性
- 性能優化

### v1.0.0 (2024-01-XX)
- 初始版本發布
- 基本業務功能實現

## 技術支持

如遇到問題，請：

1. 查看日誌文件獲取詳細錯誤信息
2. 檢查本文檔的故障排除部分
3. 聯繫開發團隊

## 開發者

蒼藍工程公司開發團隊

## 授權

版權所有 © 2024 蒼藍工程公司

---

**注意**：本系統僅供蒼藍工程公司內部使用，未經授權不得複製或分發。 