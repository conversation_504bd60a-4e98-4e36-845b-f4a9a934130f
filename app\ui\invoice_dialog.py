from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
                             QPushButton, QLineEdit, QDateEdit, QComboBox, QTextEdit,
                             QLabel, QTableWidget, QTableWidgetItem, QHeaderView, QSpinBox,
                             QDoubleSpinBox, QMessageBox, QTabWidget, QWidget, QFileDialog, QRadioButton)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager

class InvoiceDialog(QDialog):
    """發票新增/編輯對話框"""
    
    def __init__(self, parent=None, db_manager=None, invoice_data=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_data = invoice_data
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.is_from_quotation = False  # 新增標記，用於判斷是否來自報價單
        
        # 如果是編輯模式，檢查是否來自報價單
        if self.invoice_data and self.invoice_data.get('related_quotation_id'):
            self.is_from_quotation = True
            
        self.item_data = []  # 用於存儲發票項目
        
        self.init_ui()
        
        # 如果是編輯模式，載入發票數據
        if self.invoice_data:
            self.load_invoice_data()
        else:
            # 新建模式，設置默認值
            self.setup_default_values()
            
    def init_ui(self):
        # 設置對話框標題
        self.setWindowTitle(self.lang_manager.get_text('add_invoice') if not self.invoice_data else self.lang_manager.get_text('edit_invoice'))
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        
        # 創建主布局
        main_layout = QVBoxLayout(self)
        
        # 創建選項卡
        tab_widget = QTabWidget()
        
        # 創建基本信息選項卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 基本信息區域
        basic_info_group = QGroupBox(self.lang_manager.get_text('basic_info'))
        basic_info_layout = QFormLayout(basic_info_group)
        
        # 發票號
        self.invoice_no_layout = QHBoxLayout()
        self.invoice_no_edit = QLineEdit()
        self.invoice_no_edit.setReadOnly(True)  # 發票號自動生成，不允許手動修改
        self.auto_generate_btn = QPushButton(self.lang_manager.get_text('auto_generate'))
        self.auto_generate_btn.clicked.connect(self.generate_invoice_number)
        self.invoice_no_layout.addWidget(self.invoice_no_edit)
        self.invoice_no_layout.addWidget(self.auto_generate_btn)
        basic_info_layout.addRow(self.lang_manager.get_text('invoice_no'), self.invoice_no_layout)
        
        # 發票日期
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        basic_info_layout.addRow(self.lang_manager.get_text('date'), self.date_edit)
        
        # 付款到期日
        self.payment_due_edit = QDateEdit(QDate.currentDate().addDays(30))  # 默認30天後到期
        self.payment_due_edit.setCalendarPopup(True)
        basic_info_layout.addRow(self.lang_manager.get_text('payment_due'), self.payment_due_edit)
        
        # 發票狀態
        self.status_combo = QComboBox()
        self.status_combo.addItem(self.lang_manager.get_text('unpaid'))
        self.status_combo.addItem(self.lang_manager.get_text('partially_paid'))
        self.status_combo.addItem(self.lang_manager.get_text('paid'))
        basic_info_layout.addRow(self.lang_manager.get_text('payment_status'), self.status_combo)
        
        # 關聯報價單號（可選）
        self.related_quotation_combo = QComboBox()
        self.related_quotation_combo.addItem("", None)  # 空選項
        # 這裡應從數據庫加載報價單列表
        if self.db_manager:
            quotations = self.db_manager.get_quotations()
            for quotation in quotations:
                self.related_quotation_combo.addItem(
                    f"{quotation['quotation_no']} - {quotation['customer_name']}",
                    quotation['id']
                )
        basic_info_layout.addRow(self.lang_manager.get_text('related_quotation'), self.related_quotation_combo)
        
        # 客戶資訊區域
        customer_info_group = QGroupBox(self.lang_manager.get_text('customer_info'))
        customer_info_layout = QFormLayout(customer_info_group)
        
        # 客戶選擇
        self.customer_combo = QComboBox()
        self.customer_combo.addItem("", None)  # 空選項
        # 這裡應從數據庫加載客戶列表
        if self.db_manager:
            customers = self.db_manager.get_customers()
            for customer in customers:
                self.customer_combo.addItem(
                    customer['name'], 
                    customer['id']
                )
        self.customer_combo.currentIndexChanged.connect(self.customer_changed)
        customer_info_layout.addRow(self.lang_manager.get_text('customer'), self.customer_combo)
        
        # 聯繫人
        self.contact_edit = QLineEdit()
        customer_info_layout.addRow(self.lang_manager.get_text('contact_person'), self.contact_edit)
        
        # 電話
        self.phone_edit = QLineEdit()
        customer_info_layout.addRow(self.lang_manager.get_text('phone'), self.phone_edit)
        
        # 地址
        self.address_edit = QLineEdit()
        customer_info_layout.addRow(self.lang_manager.get_text('address'), self.address_edit)
        
        # 添加基本信息和客戶資訊到基本選項卡
        basic_layout.addWidget(basic_info_group)
        basic_layout.addWidget(customer_info_group)
        
        # 創建項目選項卡
        items_tab = QWidget()
        items_layout = QVBoxLayout(items_tab)
        
        # 項目表格
        self.items_table = QTableWidget(0, 7)  # 項目編號, 描述, 數量, 單位, 單價, 折扣%, 金額
        self.items_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('item_no'),
            self.lang_manager.get_text('description'),
            self.lang_manager.get_text('quantity'),
            self.lang_manager.get_text('unit'),
            self.lang_manager.get_text('price'),
            self.lang_manager.get_text('discount') + "%",
            self.lang_manager.get_text('amount')
        ])
        
        # 表格佈局
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setSelectionBehavior(self.items_table.SelectRows)
        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        items_layout.addWidget(self.items_table)
        
        # 項目操作按鈕佈局
        items_buttons_layout = QHBoxLayout()
        
        # 添加項目按鈕
        self.add_item_btn = QPushButton(self.lang_manager.get_text('add_item'))
        self.add_item_btn.setIcon(self.icon_manager.get_icon('add'))
        self.add_item_btn.clicked.connect(self.add_item)
        items_buttons_layout.addWidget(self.add_item_btn)
        
        # 編輯項目按鈕
        self.edit_item_btn = QPushButton(self.lang_manager.get_text('edit_item'))
        self.edit_item_btn.setIcon(self.icon_manager.get_icon('edit'))
        self.edit_item_btn.clicked.connect(self.edit_item)
        items_buttons_layout.addWidget(self.edit_item_btn)
        
        # 刪除項目按鈕
        self.delete_item_btn = QPushButton(self.lang_manager.get_text('delete_item'))
        self.delete_item_btn.setIcon(self.icon_manager.get_icon('delete'))
        self.delete_item_btn.clicked.connect(self.delete_item)
        items_buttons_layout.addWidget(self.delete_item_btn)
        
        items_buttons_layout.addStretch()
        items_layout.addLayout(items_buttons_layout)
        
        # 小計和折扣區域
        totals_layout = QFormLayout()
        
        # 小計
        self.subtotal_label = QLabel("0.00")
        totals_layout.addRow(self.lang_manager.get_text('subtotal'), self.subtotal_label)
        
        # 折扣
        discount_layout = QHBoxLayout()
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix("%")
        self.discount_spin.valueChanged.connect(self.update_totals)
        self.discount_amount_label = QLabel("0.00")
        discount_layout.addWidget(self.discount_spin)
        discount_layout.addWidget(self.discount_amount_label)
        totals_layout.addRow(self.lang_manager.get_text('discount'), discount_layout)
        
        # 總計
        self.total_label = QLabel("0.00")
        self.total_label.setObjectName("total-amount")  # 為自定義樣式
        totals_layout.addRow(self.lang_manager.get_text('total'), self.total_label)
        
        items_layout.addLayout(totals_layout)
        
        # 創建備註和條款選項卡
        notes_tab = QWidget()
        notes_layout = QVBoxLayout(notes_tab)
        
        # 備註
        notes_group = QGroupBox(self.lang_manager.get_text('notes'))
        notes_group_layout = QVBoxLayout(notes_group)
        self.notes_edit = QTextEdit()
        notes_group_layout.addWidget(self.notes_edit)
        notes_layout.addWidget(notes_group)
        
        # 條款和條件
        terms_group = QGroupBox(self.lang_manager.get_text('terms_and_conditions'))
        terms_group_layout = QVBoxLayout(terms_group)
        self.terms_edit = QTextEdit()
        self.terms_edit.setPlaceholderText(self.lang_manager.get_text('default_terms'))
        terms_group_layout.addWidget(self.terms_edit)
        notes_layout.addWidget(terms_group)
        
        # 添加所有選項卡
        tab_widget.addTab(basic_tab, self.lang_manager.get_text('basic_info'))
        tab_widget.addTab(items_tab, self.lang_manager.get_text('items'))
        tab_widget.addTab(notes_tab, self.lang_manager.get_text('notes_and_terms'))
        
        main_layout.addWidget(tab_widget)
        
        # 底部按鈕區域
        buttons_layout = QHBoxLayout()
        
        # 保存按鈕
        self.btn_save = QPushButton(self.lang_manager.get_text('save'))
        self.btn_save.setIcon(self.icon_manager.get_icon('save'))
        self.btn_save.clicked.connect(self.accept)
        
        # 預覽按鈕
        self.btn_preview = QPushButton(self.lang_manager.get_text('preview'))
        self.btn_preview.setIcon(self.icon_manager.get_icon('preview'))
        self.btn_preview.clicked.connect(self.preview_invoice)
        
        # 匯出PDF按鈕
        self.btn_export_pdf = QPushButton(self.lang_manager.get_text('export_pdf'))
        self.btn_export_pdf.setIcon(self.icon_manager.get_icon('pdf'))
        self.btn_export_pdf.clicked.connect(self.export_pdf)
        
        # 取消按鈕
        self.btn_cancel = QPushButton(self.lang_manager.get_text('cancel'))
        self.btn_cancel.setIcon(self.icon_manager.get_icon('cancel'))
        self.btn_cancel.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_preview)
        buttons_layout.addWidget(self.btn_export_pdf)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_cancel)
        
        main_layout.addLayout(buttons_layout)
    
    def generate_invoice_number(self):
        """生成新的發票編號"""
        if not self.db_manager:
            return
        
        # 生成新的發票編號
        new_invoice_no = self.db_manager.generate_new_invoice_number()
        self.invoice_no_edit.setText(new_invoice_no)
    
    def customer_changed(self, index):
        """當客戶選擇改變時，自動填充客戶資訊"""
        if index <= 0:  # 未選擇有效客戶
            self.contact_edit.clear()
            self.phone_edit.clear()
            self.address_edit.clear()
            return
        
        customer_id = self.customer_combo.itemData(index)
        if not self.db_manager or not customer_id:
            return
        
        # 從數據庫獲取客戶資訊
        customer = self.db_manager.get_customer(customer_id)
        if customer:
            self.contact_edit.setText(customer.get('contact_person', ''))
            self.phone_edit.setText(customer.get('phone', ''))
            self.address_edit.setText(customer.get('address', ''))
    
    def add_item(self):
        """添加發票項目"""
        dialog = InvoiceItemDialog(self, self.db_manager)
        if dialog.exec_():
            item_data = dialog.get_data()
            
            # 設置項目編號
            item_data['item_no'] = len(self.item_data) + 1
            
            # 添加到項目數據列表
            self.item_data.append(item_data)
            
            # 更新表格
            self.update_items_table()
            
            # 更新總計
            self.update_totals()
    
    def edit_item(self):
        """編輯發票項目"""
        selected_rows = self.items_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        row = selected_rows[0].row()
        item_index = row
        
        if item_index >= len(self.item_data):
            return
        
        item = self.item_data[item_index]
        dialog = InvoiceItemDialog(self, self.db_manager, item)
        
        if dialog.exec_():
            updated_item = dialog.get_data()
            # 保持原有的項目編號
            updated_item['item_no'] = item['item_no']
            
            # 更新項目數據
            self.item_data[item_index] = updated_item
            
            # 更新表格
            self.update_items_table()
            
            # 更新總計
            self.update_totals()
    
    def delete_item(self):
        """刪除發票項目"""
        selected_rows = self.items_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        row = selected_rows[0].row()
        item_index = row
        
        if item_index >= len(self.item_data):
            return
        
        # 刪除項目
        self.item_data.pop(item_index)
        
        # 重新編號
        for i, item in enumerate(self.item_data):
            item['item_no'] = i + 1
        
        # 更新表格
        self.update_items_table()
        
        # 更新總計
        self.update_totals()
    
    def update_items_table(self):
        """更新項目表格"""
        self.items_table.setRowCount(0)
        for item in self.item_data:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)
            
            # 項目編號
            self.items_table.setItem(row, 0, QTableWidgetItem(str(item.get('item_no', ''))))
            
            # 描述
            self.items_table.setItem(row, 1, QTableWidgetItem(item.get('description', '')))
            
            # 數量
            self.items_table.setItem(row, 2, QTableWidgetItem(str(item.get('quantity', 0))))
            
            # 單位
            self.items_table.setItem(row, 3, QTableWidgetItem(item.get('unit', '')))
            
            # 單價
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item.get('unit_price', 0):.2f}"))
            
            # 折扣%
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{item.get('discount', 0):.2f}%"))
            
            # 金額
            amount = item.get('quantity', 0) * item.get('unit_price', 0) * (1 - item.get('discount', 0) / 100)
            self.items_table.setItem(row, 6, QTableWidgetItem(f"{amount:.2f}"))
    
    def update_totals(self):
        """更新總計金額"""
        # 計算小計
        subtotal = 0
        for item in self.item_data:
            amount = item.get('quantity', 0) * item.get('unit_price', 0) * (1 - item.get('discount', 0) / 100)
            subtotal += amount
        
        self.subtotal_label.setText(f"{subtotal:.2f}")
        
        # 計算折扣
        discount_percentage = self.discount_spin.value()
        discount_amount = subtotal * discount_percentage / 100
        self.discount_amount_label.setText(f"{discount_amount:.2f}")
        
        # 計算總計
        total = subtotal - discount_amount
        self.total_label.setText(f"{total:.2f}")
    
    def load_invoice_data(self):
        """載入發票數據"""
        if not self.invoice_data:
            return
            
        # 設置基本信息
        self.invoice_no_edit.setText(self.invoice_data.get('invoice_no', ''))
        self.date_edit.setDate(QDate.fromString(self.invoice_data.get('date', ''), "yyyy-MM-dd"))
        self.payment_due_edit.setDate(QDate.fromString(self.invoice_data.get('payment_due', ''), "yyyy-MM-dd"))
        self.status_combo.setCurrentText(self.invoice_data.get('payment_status', ''))
        
        # 設置關聯報價單
        quotation_id = self.invoice_data.get('related_quotation_id')
        if quotation_id:
            for i in range(self.related_quotation_combo.count()):
                if self.related_quotation_combo.itemData(i) == quotation_id:
                    self.related_quotation_combo.setCurrentIndex(i)
                    break
        
        # 設置客戶信息
        customer_id = self.invoice_data.get('customer_id')
        if customer_id:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break
        
        self.contact_edit.setText(self.invoice_data.get('contact_person', ''))
        self.phone_edit.setText(self.invoice_data.get('phone', ''))
        self.address_edit.setText(self.invoice_data.get('address', ''))
        
        # 設置項目
        self.item_data = self.invoice_data.get('items', [])
        self.update_items_table()
        
        # 設置折扣
        self.discount_spin.setValue(self.invoice_data.get('discount_percentage', 0))
        
        # 設置備註和條款
        self.notes_edit.setPlainText(self.invoice_data.get('notes', ''))
        self.terms_edit.setPlainText(self.invoice_data.get('terms', ''))
        
        # 更新總金額
        self.update_totals()
        
        # 如果是來自報價單的發票，鎖定相關欄位
        if self.is_from_quotation:
            self.setReadOnly(False)  # 先設置為可編輯
            self.customer_combo.setEnabled(False)  # 鎖定客戶選擇
            self.related_quotation_combo.setEnabled(False)  # 鎖定報價單選擇
            self.contact_edit.setReadOnly(True)  # 鎖定聯絡人
            self.phone_edit.setReadOnly(True)  # 鎖定電話
            self.address_edit.setReadOnly(True)  # 鎖定地址
    
    def setup_default_values(self):
        """設置默認值（用於新建模式）"""
        # 生成新的發票編號
        self.generate_invoice_number()
        
        # 設置默認的發票條款
        self.terms_edit.setPlainText("1. 請在到期日前付款\n2. 逾期付款將收取額外費用\n3. 所有價格均不包含稅金")
    
    def setReadOnly(self, read_only=True):
        """設置對話框為只讀模式"""
        # 基本信息
        self.invoice_no_edit.setReadOnly(True)  # 發票號始終是只讀的
        self.auto_generate_btn.setEnabled(not read_only)
        self.date_edit.setReadOnly(read_only)
        self.payment_due_edit.setReadOnly(read_only)
        self.status_combo.setEnabled(not read_only)
        
        # 如果是來自報價單的發票，鎖定相關報價單選擇
        if self.is_from_quotation:
            self.related_quotation_combo.setEnabled(False)
        else:
            self.related_quotation_combo.setEnabled(not read_only)
        
        # 客戶信息 - 如果是來自報價單的發票，鎖定客戶相關欄位
        if self.is_from_quotation:
            self.customer_combo.setEnabled(False)
            self.contact_edit.setReadOnly(True)
            self.phone_edit.setReadOnly(True)
            self.address_edit.setReadOnly(True)
        else:
            self.customer_combo.setEnabled(not read_only)
            self.contact_edit.setReadOnly(read_only)
            self.phone_edit.setReadOnly(read_only)
            self.address_edit.setReadOnly(read_only)
        
        # 項目操作
        self.add_item_btn.setEnabled(not read_only)
        self.edit_item_btn.setEnabled(not read_only)
        self.delete_item_btn.setEnabled(not read_only)
        
        # 折扣
        self.discount_spin.setReadOnly(read_only)
        
        # 備註和條款
        self.notes_edit.setReadOnly(read_only)
        self.terms_edit.setReadOnly(read_only)
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        # 這裡實現語言切換時的界面文本更新
        self.setWindowTitle(self.lang_manager.get_text('add_invoice') if not self.invoice_data else self.lang_manager.get_text('edit_invoice'))
        
        # 更新按鈕文字
        self.btn_save.setText(self.lang_manager.get_text('save'))
        self.btn_preview.setText(self.lang_manager.get_text('preview'))
        self.btn_export_pdf.setText(self.lang_manager.get_text('export_pdf'))
        self.btn_cancel.setText(self.lang_manager.get_text('cancel'))
        self.add_item_btn.setText(self.lang_manager.get_text('add_item'))
        self.edit_item_btn.setText(self.lang_manager.get_text('edit_item'))
        self.delete_item_btn.setText(self.lang_manager.get_text('delete_item'))
        self.auto_generate_btn.setText(self.lang_manager.get_text('auto_generate'))
        
        # 更新表格標題
        self.items_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('item_no'),
            self.lang_manager.get_text('description'),
            self.lang_manager.get_text('quantity'),
            self.lang_manager.get_text('unit'),
            self.lang_manager.get_text('price'),
            self.lang_manager.get_text('discount') + "%",
            self.lang_manager.get_text('amount')
        ])
        
        # 更新其他標籤文字...
    
    def preview_invoice(self):
        """預覽發票"""
        QMessageBox.information(
            self, 
            self.lang_manager.get_text('information'),
            self.lang_manager.get_text('preview_not_implemented')
        )
    
    def export_pdf(self):
        """匯出PDF"""
        try:
            # 獲取發票數據
            invoice_data = self.get_data()
            
            # 準備默認文件名
            default_filename = f"Invoice_{invoice_data['invoice_no'].replace('/', '_')}.pdf"
            
            # 顯示文件保存對話框
            output_path, _ = QFileDialog.getSaveFileName(
                self,
                self.lang_manager.get_text('export_pdf'),
                default_filename,
                "PDF Files (*.pdf)"
            )
            
            # 如果用戶取消，則返回
            if not output_path:
                return
            
            # 使用統一的PDF管理器
            from app.utils.pdf_manager import PDFManager
            pdf_manager = PDFManager()
            pdf_file = pdf_manager.generate_invoice_pdf(invoice_data, output_path)
            
            if pdf_file:
                # 顯示成功消息
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    f"{self.lang_manager.get_text('export_success')}\n{pdf_file}"
                )
        except ImportError:
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('information'),
                self.lang_manager.get_text('export_pdf_not_implemented')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('export_pdf_error')}: {str(e)}"
            )
    
    def get_data(self):
        """獲取對話框的數據"""
        customer_index = self.customer_combo.currentIndex()
        customer_id = self.customer_combo.itemData(customer_index) if customer_index > 0 else None
        
        # 獲取關聯報價單ID
        quotation_index = self.related_quotation_combo.currentIndex()
        quotation_id = self.related_quotation_combo.itemData(quotation_index) if quotation_index > 0 else None
        
        # 計算總金額
        subtotal = 0
        for item in self.item_data:
            amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
            subtotal += amount
            
        discount_percentage = self.discount_spin.value()
        discount_amount = subtotal * discount_percentage / 100
        total_amount = subtotal - discount_amount
        
        data = {
            'invoice_no': self.invoice_no_edit.text(),
            'date': self.date_edit.date().toString("yyyy-MM-dd"),
            'payment_due': self.payment_due_edit.date().toString("yyyy-MM-dd"),
            'payment_status': self.status_combo.currentText(),
            'related_quotation_id': quotation_id,
            'customer_id': customer_id,
            'customer_name': self.customer_combo.currentText(),
            'contact_person': self.contact_edit.text(),
            'phone': self.phone_edit.text(),
            'address': self.address_edit.text(),
            'subtotal': subtotal,
            'discount_percentage': discount_percentage,
            'discount_amount': discount_amount,
            'total_amount': total_amount,
            'notes': self.notes_edit.toPlainText(),
            'terms': self.terms_edit.toPlainText(),
            'items': self.item_data
        }
        
        return data


class InvoiceItemDialog(QDialog):
    """發票項目對話框"""
    
    def __init__(self, parent=None, db_manager=None, item_data=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.item_data = item_data  # 如果提供，則為編輯模式
        self.lang_manager = LanguageManager()
        self.init_ui()
        
        # 如果是編輯模式，設置初始值
        if self.item_data:
            self.load_item_data()
    
    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('add_item') if not self.item_data else self.lang_manager.get_text('edit_item'))
        self.setMinimumWidth(500)
        self.setMinimumHeight(450)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 項目類型選擇
        type_group = QGroupBox(self.lang_manager.get_text('item_type'))
        type_layout = QHBoxLayout(type_group)
        
        self.product_radio = QRadioButton(self.lang_manager.get_text('product'))
        self.service_radio = QRadioButton(self.lang_manager.get_text('service'))
        
        type_layout.addWidget(self.product_radio)
        type_layout.addWidget(self.service_radio)
        
        # 默認選擇產品
        self.product_radio.setChecked(True)
        
        # 連接信號槽
        self.product_radio.toggled.connect(self.toggle_item_type)
        
        layout.addWidget(type_group)
        
        # 產品表單
        self.product_widget = QWidget()
        product_layout = QFormLayout(self.product_widget)
        
        # 產品選擇
        self.product_combo = QComboBox()
        self.product_combo.addItem("", None)  # 空選項
        # 這裡應從數據庫加載產品列表
        if self.db_manager:
            products = self.db_manager.get_products()
            for product in products:
                self.product_combo.addItem(
                    product['name'], 
                    product['id']
                )
        self.product_combo.currentIndexChanged.connect(self.product_changed)
        product_layout.addRow(self.lang_manager.get_text('product'), self.product_combo)
        
        # 產品描述
        self.product_description_edit = QLineEdit()
        product_layout.addRow(self.lang_manager.get_text('description'), self.product_description_edit)
        
        layout.addWidget(self.product_widget)
        
        # 服務表單
        self.service_widget = QWidget()
        service_layout = QFormLayout(self.service_widget)
        
        # 服務名稱
        self.service_name_edit = QLineEdit()
        service_layout.addRow(self.lang_manager.get_text('service_name'), self.service_name_edit)
        
        # 服務描述（更大的輸入區域）
        self.service_description_edit = QTextEdit()
        self.service_description_edit.setMinimumHeight(150)  # 設置更大的高度
        service_layout.addRow(self.lang_manager.get_text('service_description'), self.service_description_edit)
        
        layout.addWidget(self.service_widget)
        
        # 共用表單
        common_widget = QWidget()
        common_layout = QFormLayout(common_widget)
        
        # 數量
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.valueChanged.connect(self.update_amount)
        common_layout.addRow(self.lang_manager.get_text('quantity'), self.quantity_spin)
        
        # 單位
        self.unit_edit = QLineEdit()
        common_layout.addRow(self.lang_manager.get_text('unit'), self.unit_edit)
        
        # 單價
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSingleStep(0.01)
        self.unit_price_spin.valueChanged.connect(self.update_amount)
        common_layout.addRow(self.lang_manager.get_text('price'), self.unit_price_spin)
        
        # 折扣%
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix("%")
        self.discount_spin.valueChanged.connect(self.update_amount)
        common_layout.addRow(self.lang_manager.get_text('discount'), self.discount_spin)
        
        # 金額
        self.amount_label = QLabel("0.00")
        common_layout.addRow(self.lang_manager.get_text('amount'), self.amount_label)
        
        layout.addWidget(common_widget)
        
        # 初始顯示產品，隱藏服務
        self.toggle_item_type()
        
        # 按鈕布局
        button_layout = QHBoxLayout()
        
        # 確定按鈕
        self.ok_button = QPushButton(self.lang_manager.get_text('save'))
        self.ok_button.clicked.connect(self.accept)
        
        # 取消按鈕
        self.cancel_button = QPushButton(self.lang_manager.get_text('cancel'))
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def toggle_item_type(self):
        """切換項目類型顯示"""
        is_product = self.product_radio.isChecked()
        self.product_widget.setVisible(is_product)
        self.service_widget.setVisible(not is_product)
    
    def product_changed(self, index):
        """當產品選擇改變時，自動填充產品資訊"""
        if index <= 0:  # 未選擇有效產品
            self.product_description_edit.clear()
            self.unit_edit.clear()
            self.unit_price_spin.setValue(0)
            return
        
        product_id = self.product_combo.itemData(index)
        if not self.db_manager or not product_id:
            return
        
        # 從數據庫獲取產品資訊
        product = self.db_manager.get_product(product_id)
        if product:
            self.product_description_edit.setText(product.get('description', product.get('name', '')))
            self.unit_edit.setText(product.get('unit', ''))
            self.unit_price_spin.setValue(product.get('price', 0))
    
    def update_amount(self):
        """更新金額"""
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        discount = self.discount_spin.value()
        
        amount = quantity * unit_price * (1 - discount / 100)
        self.amount_label.setText(f"{amount:.2f}")
    
    def load_item_data(self):
        """載入項目數據（用於編輯模式）"""
        if not self.item_data:
            return
        
        # 判斷是產品還是服務
        is_product = self.item_data.get('product_id') is not None
        
        # 設置項目類型
        if is_product:
            self.product_radio.setChecked(True)
            # 設置產品
            if 'product_id' in self.item_data:
                for i in range(self.product_combo.count()):
                    if self.product_combo.itemData(i) == self.item_data['product_id']:
                        self.product_combo.setCurrentIndex(i)
                        break
            
            # 設置產品描述
            self.product_description_edit.setText(self.item_data.get('description', ''))
        else:
            self.service_radio.setChecked(True)
            # 設置服務名稱和描述
            service_name = self.item_data.get('description', '').split('\n')[0] if '\n' in self.item_data.get('description', '') else self.item_data.get('description', '')
            self.service_name_edit.setText(service_name)
            
            # 如果描述中有多行，把第一行之後的內容作為服務描述
            if '\n' in self.item_data.get('description', ''):
                service_desc = '\n'.join(self.item_data.get('description', '').split('\n')[1:])
                self.service_description_edit.setText(service_desc)
        
        # 設置共用項目詳情
        self.quantity_spin.setValue(self.item_data.get('quantity', 1))
        self.unit_edit.setText(self.item_data.get('unit', ''))
        self.unit_price_spin.setValue(self.item_data.get('unit_price', 0))
        self.discount_spin.setValue(self.item_data.get('discount', 0))
        
        # 更新金額
        self.update_amount()
        
        # 切換顯示
        self.toggle_item_type()
    
    def get_data(self):
        """獲取對話框的數據"""
        is_product = self.product_radio.isChecked()
        
        if is_product:
            # 產品項目
            product_index = self.product_combo.currentIndex()
            product_id = self.product_combo.itemData(product_index) if product_index > 0 else None
            description = self.product_description_edit.text()
            name = self.product_combo.currentText() if product_index > 0 else ''
        else:
            # 服務項目
            product_id = None
            name = self.service_name_edit.text()
            
            # 服務描述可能有多行，將服務名稱和描述合併
            service_description = self.service_description_edit.toPlainText()
            if service_description:
                description = f"{name}\n{service_description}"
            else:
                description = name
        
        data = {
            'product_id': product_id,
            'product_name': name,
            'description': description,
            'quantity': self.quantity_spin.value(),
            'unit': self.unit_edit.text(),
            'unit_price': self.unit_price_spin.value(),
            'discount': self.discount_spin.value()
        }
        
        return data
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        # 更新窗口標題
        self.setWindowTitle(self.lang_manager.get_text('add_item') if not self.item_data else self.lang_manager.get_text('edit_item'))
        
        # 更新項目類型群組標題
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.widget() and isinstance(item.widget(), QGroupBox):
                group_box = item.widget()
                if 'item_type' in group_box.title().lower() or '項目類型' in group_box.title():
                    group_box.setTitle(self.lang_manager.get_text('item_type'))
        
        # 更新項目類型
        self.product_radio.setText(self.lang_manager.get_text('product'))
        self.service_radio.setText(self.lang_manager.get_text('service'))
        
        # 更新產品區域表單標籤
        product_layout = self.product_widget.layout()
        if product_layout:
            for i in range(product_layout.rowCount()):
                label_item = product_layout.itemAt(i, QFormLayout.LabelRole)
                if label_item and label_item.widget():
                    label = label_item.widget()
                    if isinstance(label, QLabel):
                        text = label.text()
                        if 'product' in text.lower() or '產品' in text:
                            label.setText(self.lang_manager.get_text('product'))
                        elif 'description' in text.lower() or '描述' in text:
                            label.setText(self.lang_manager.get_text('description'))
        
        # 更新服務區域表單標籤
        service_layout = self.service_widget.layout()
        if service_layout:
            for i in range(service_layout.rowCount()):
                label_item = service_layout.itemAt(i, QFormLayout.LabelRole)
                if label_item and label_item.widget():
                    label = label_item.widget()
                    if isinstance(label, QLabel):
                        text = label.text()
                        if 'service_name' in text.lower() or '服務名稱' in text:
                            label.setText(self.lang_manager.get_text('service_name'))
                        elif 'service_description' in text.lower() or '服務描述' in text:
                            label.setText(self.lang_manager.get_text('service_description'))
        
        # 更新共用控件區域表單標籤
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.widget() and isinstance(item.widget(), QWidget):
                if hasattr(item.widget(), 'layout') and isinstance(item.widget().layout(), QFormLayout):
                    form_layout = item.widget().layout()
                    for j in range(form_layout.rowCount()):
                        label_item = form_layout.itemAt(j, QFormLayout.LabelRole)
                        if label_item and label_item.widget():
                            label = label_item.widget()
                            if isinstance(label, QLabel):
                                text = label.text()
                                if 'quantity' in text.lower() or '數量' in text:
                                    label.setText(self.lang_manager.get_text('quantity'))
                                elif 'unit' in text.lower() or '單位' in text:
                                    label.setText(self.lang_manager.get_text('unit'))
                                elif 'price' in text.lower() or '價格' in text or '單價' in text:
                                    label.setText(self.lang_manager.get_text('unit_price'))
                                elif 'discount' in text.lower() or '折扣' in text:
                                    label.setText(self.lang_manager.get_text('discount'))
                                elif 'amount' in text.lower() or '金額' in text:
                                    label.setText(self.lang_manager.get_text('amount'))
        
        # 更新按鈕文字
        self.ok_button.setText(self.lang_manager.get_text('save'))
        self.cancel_button.setText(self.lang_manager.get_text('cancel')) 