from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
                           QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QFont
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager
from app.utils.logger import system_logger

class LoginDialog(QDialog):
    """登錄對話框"""
    
    # 自定義信號，登錄成功時發射
    login_success = pyqtSignal(dict)
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.user_data = None  # 存儲登錄用戶數據
        
        # 移除無邊框設置，使窗口更容易被看見
        # self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFixedSize(400, 300)
        self.setWindowTitle("登錄")
        
        # 主佈局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 標題區域
        title_layout = QHBoxLayout()
        
        # 添加圖標
        logo_label = QLabel()
        logo_pixmap = QPixmap(":/icons/app_icon.png")
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        title_layout.addWidget(logo_label)
        
        # 標題文字
        title_label = QLabel(self.lang_manager.get_text('app_title'))
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        title_layout.addWidget(title_label, 1)
        
        # 關閉按鈕
        close_button = QPushButton()
        close_button.setIcon(self.icon_manager.get_icon('close'))
        close_button.setFlat(True)
        close_button.clicked.connect(self.reject)
        title_layout.addWidget(close_button)
        
        main_layout.addLayout(title_layout)
        main_layout.addSpacing(20)
        
        # 登錄表單
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        
        # 用戶名
        username_label = QLabel(self.lang_manager.get_text('username'))
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(self.lang_manager.get_text('enter_username'))
        form_layout.addRow(username_label, self.username_edit)
        
        # 密碼
        password_label = QLabel(self.lang_manager.get_text('password'))
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)  # 密碼模式
        self.password_edit.setPlaceholderText(self.lang_manager.get_text('enter_password'))
        form_layout.addRow(password_label, self.password_edit)
        
        main_layout.addLayout(form_layout)
        main_layout.addSpacing(30)
        
        # 按鈕區域
        buttons_layout = QHBoxLayout()
        
        # 登錄按鈕
        self.login_button = QPushButton(self.lang_manager.get_text('login'))
        self.login_button.setIcon(self.icon_manager.get_icon('login'))
        self.login_button.clicked.connect(self.do_login)
        buttons_layout.addWidget(self.login_button)
        
        # 取消按鈕
        self.cancel_button = QPushButton(self.lang_manager.get_text('cancel'))
        self.cancel_button.setIcon(self.icon_manager.get_icon('cancel'))
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
        main_layout.addStretch()
        
        # 版本信息區域
        footer_layout = QHBoxLayout()
        version_label = QLabel("v1.0.0")
        version_label.setAlignment(Qt.AlignLeft)
        footer_layout.addWidget(version_label)
        
        company_label = QLabel("© 2023 蒼藍工程公司")
        company_label.setAlignment(Qt.AlignRight)
        footer_layout.addWidget(company_label)
        
        main_layout.addLayout(footer_layout)
        
        # 設置樣式表
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            QPushButton {
                padding: 8px 16px;
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
        """)
        
        # 設置輸入焦點
        self.username_edit.setFocus()
        
        # 連接回車鍵
        self.username_edit.returnPressed.connect(lambda: self.password_edit.setFocus())
        self.password_edit.returnPressed.connect(self.do_login)
    
    def do_login(self):
        """執行登錄操作"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('username_password_required')
            )
            return
        
        if not self.db_manager:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('database_connection_error')
            )
            return
            
        conn_opened = False
        
        try:
            # 確保資料庫連接
            conn_opened = self.db_manager.connect()
            if not conn_opened or not self.db_manager.cursor:
                raise Exception("無法建立資料庫連接或游標為空")
                
            # 添加調試輸出
            system_logger.info(f"嘗試登入: 用戶名 = {username}")
            
            # 使用 SHA-256 生成密碼雜湊
            import hashlib
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # 直接查詢資料庫
            self.db_manager.cursor.execute("""
                SELECT id, username, full_name, role, status
                FROM users
                WHERE username = ? AND password_hash = ? AND status = 'active'
            """, (username, password_hash))
            
            user = self.db_manager.cursor.fetchone()
            
            if user:
                # 更新最後登錄時間
                import datetime
                self.db_manager.cursor.execute("""
                    UPDATE users
                    SET last_login = ?
                    WHERE id = ?
                """, (datetime.datetime.now().isoformat(), user[0]))
                self.db_manager.conn.commit()
                
                # 返回用戶資訊
                user_data = {
                    'id': user[0],
                    'username': user[1],
                    'full_name': user[2],
                    'role': user[3],
                    'status': user[4]
                }
                
                system_logger.info(f"登入成功: {user_data['username']}")
                
                self.user_data = user_data
                
                # 關閉連接
                self.db_manager.disconnect()
                    
                self.login_success.emit(self.user_data)  # 發射登錄成功信號
                self.accept()  # 關閉對話框並返回接受
            else:
                # 添加調試輸出
                system_logger.info(f"登入失敗: 用戶 {username} 的憑據無效")
                
                # 關閉連接
                self.db_manager.disconnect()
                    
                QMessageBox.critical(
                    self,
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('invalid_credentials')
                )
                self.password_edit.clear()
                self.password_edit.setFocus()
                
        except Exception as e:
            system_logger.error(f"登入過程發生錯誤: {str(e)}")
            
            # 確保連接關閉
            if self.db_manager:
                self.db_manager.disconnect()
                
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('database_connection_error')}\n錯誤: {str(e)}"
            )
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        
        self.setWindowTitle(self.lang_manager.get_text('login'))
        self.login_button.setText(self.lang_manager.get_text('login'))
        self.cancel_button.setText(self.lang_manager.get_text('cancel'))
        
        # 更新表單標籤
        form_layout = None
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.layout() and isinstance(item.layout(), QFormLayout):
                form_layout = item.layout()
                break
        
        if form_layout:
            for i in range(form_layout.rowCount()):
                label_item = form_layout.itemAt(i, QFormLayout.LabelRole)
                if label_item and label_item.widget():
                    label = label_item.widget()
                    if isinstance(label, QLabel):
                        text = label.text()
                        if 'username' in text.lower() or '用戶名' in text:
                            label.setText(self.lang_manager.get_text('username'))
                        elif 'password' in text.lower() or '密碼' in text:
                            label.setText(self.lang_manager.get_text('password')) 