#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import configparser
from datetime import datetime
from typing import Dict, List, Optional, Any
import tempfile
import base64

try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    print("WeasyPrint not available. Please install: pip install weasyprint")

from app.utils.logger import system_logger

class PDFGeneratorWeasy:
    """基於WeasyPrint的專業PDF生成器
    
    提供更好的CSS控制和PDF輸出質量，支持複雜的佈局和字體控制
    """
    
    def __init__(self):
        """初始化PDF生成器"""
        if not WEASYPRINT_AVAILABLE:
            raise ImportError("WeasyPrint is required but not installed. Please run: pip install weasyprint")
        
        # 讀取配置
        self.config = configparser.ConfigParser()
        self.config.read('config/settings.ini', encoding='utf-8')
        
        # 字體配置
        self.font_config = FontConfiguration()
        
        # 創建輸出目錄
        os.makedirs('output', exist_ok=True)
        
        system_logger.info("WeasyPrint PDF生成器初始化完成")
    
    def generate_quotation_pdf(self, quotation_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成報價單PDF
        
        Args:
            quotation_data: 報價單數據
            output_path: 輸出文件路徑，如果為None則使用默認路徑
            
        Returns:
            生成的PDF文件路徑
        """
        try:
            # 確定輸出路徑
            if output_path is None:
                quotation_no = quotation_data.get('quotation_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/Quotation_WeasyPrint_{quotation_no}.pdf"
            
            # 生成HTML內容
            html_content = self._generate_quotation_html(quotation_data)
            
            # 創建CSS樣式
            css_content = self._get_professional_css()
            
            # 生成PDF
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=self.font_config)
            
            html_doc.write_pdf(output_path, stylesheets=[css_doc], font_config=self.font_config)
            
            system_logger.info(f"成功生成報價單PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成報價單PDF時出錯: {str(e)}")
            raise
    
    def generate_invoice_pdf(self, invoice_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成發票PDF"""
        try:
            if output_path is None:
                invoice_no = invoice_data.get('invoice_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/Invoice_WeasyPrint_{invoice_no}.pdf"
            
            # 修改標題為發票
            invoice_data_copy = invoice_data.copy()
            invoice_data_copy['document_type'] = 'INVOICE'
            
            html_content = self._generate_quotation_html(invoice_data_copy)
            css_content = self._get_professional_css()
            
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=self.font_config)
            
            html_doc.write_pdf(output_path, stylesheets=[css_doc], font_config=self.font_config)
            
            system_logger.info(f"成功生成發票PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成發票PDF時出錯: {str(e)}")
            raise
    
    def generate_delivery_note_pdf(self, delivery_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成送貨單PDF"""
        try:
            if output_path is None:
                delivery_no = delivery_data.get('delivery_note_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/DeliveryNote_WeasyPrint_{delivery_no}.pdf"
            
            # 修改標題為送貨單
            delivery_data_copy = delivery_data.copy()
            delivery_data_copy['document_type'] = 'DELIVERY NOTE'
            
            html_content = self._generate_quotation_html(delivery_data_copy)
            css_content = self._get_professional_css()
            
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=self.font_config)
            
            html_doc.write_pdf(output_path, stylesheets=[css_doc], font_config=self.font_config)
            
            system_logger.info(f"成功生成送貨單PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成送貨單PDF時出錯: {str(e)}")
            raise
    
    def _calculate_pages_needed(self, items: List[Dict]) -> int:
        """計算需要的頁數"""
        items_per_first_page = 5  # 第一頁可容納5個項目
        items_per_other_page = 6  # 其他頁可容納6個項目
        
        if len(items) <= items_per_first_page:
            return 1
        
        remaining_items = len(items) - items_per_first_page
        additional_pages = (remaining_items + items_per_other_page - 1) // items_per_other_page
        return 1 + additional_pages
    
    def _generate_quotation_html(self, data: Dict[str, Any]) -> str:
        """生成報價單HTML內容"""
        # 獲取公司信息
        company_name_cn = self.config.get('CompanyInfo', 'name_cn', fallback='蒼藍工程公司')
        company_name_en = self.config.get('CompanyInfo', 'name_en', fallback='H.B ENGINEERING COMPANY')
        company_nature = self.config.get('CompanyInfo', 'nature_en', fallback='Electrical Engineering')
        phone = self.config.get('CompanyInfo', 'phone', fallback='60173180')
        fax = self.config.get('CompanyInfo', 'fax', fallback='60173180')
        
        # 文檔類型
        document_type = data.get('document_type', 'QUOTATION')
        
        # 計算總頁數
        items = data.get('items', [])
        total_pages = self._calculate_pages_needed(items)
        
        # 生成所有頁面的HTML
        pages_html = ""
        items_per_first_page = 5
        items_per_other_page = 6
        
        for page_num in range(1, total_pages + 1):
            # 計算當前頁面的項目範圍
            if page_num == 1:
                start_idx = 0
                end_idx = min(items_per_first_page, len(items))
            else:
                start_idx = items_per_first_page + (page_num - 2) * items_per_other_page
                end_idx = min(start_idx + items_per_other_page, len(items))
            
            current_page_items = items[start_idx:end_idx]
            
            # 生成當前頁面
            page_html = self._generate_page_html(
                data, current_page_items, page_num, total_pages,
                company_name_cn, company_name_en, company_nature,
                phone, fax, document_type
            )
            
            pages_html += page_html
        
        # 完整的HTML文檔
        html_content = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{document_type} - {data.get('quotation_no', data.get('invoice_no', data.get('delivery_note_no', '')))}</title>
</head>
<body>
    {pages_html}
</body>
</html>"""
        
        return html_content
    
    def _generate_page_html(self, data: Dict[str, Any], page_items: List[Dict], 
                           page_num: int, total_pages: int, 
                           company_name_cn: str, company_name_en: str, company_nature: str,
                           phone: str, fax: str, document_type: str) -> str:
        """生成單個頁面的HTML"""
        
        # 頁首HTML
        header_html = f"""
        <div class="page-header">
            <div class="company-info-left">
                <div class="company-name-cn">{company_name_cn}</div>
                <div class="company-name-en">{company_name_en}</div>
                <div class="company-nature">{company_nature}</div>
            </div>
            <div class="customer-info-right">
                <div class="customer-info-title">CUSTOMER INFORMATION.</div>
                <div class="customer-details">
                    <div class="customer-name">{data.get('customer_name', '')}</div>
                    <div class="contact-person">{data.get('contact_person', '')}</div>
                    <div class="customer-address">{data.get('address', '')}</div>
                    <div class="contact-details">
                        Tel: {data.get('phone', '')} | Fax: {data.get('fax', '')}
                    </div>
                </div>
            </div>
        </div>
        """
        
        # 文檔信息HTML（僅第一頁顯示）
        document_info_html = ""
        if page_num == 1:
            document_no = data.get('quotation_no', data.get('invoice_no', data.get('delivery_note_no', '')))
            document_info_html = f"""
            <div class="document-metadata">
                <table class="metadata-table">
                    <tr>
                        <td class="label-cell">DATE:</td>
                        <td class="value-cell">{data.get('date', datetime.now().strftime('%B %d, %Y')).upper()}</td>
                        <td class="label-cell">TEL:</td>
                        <td class="value-cell">{phone}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">NO.:</td>
                        <td class="value-cell">{document_no}</td>
                        <td class="label-cell">FAX:</td>
                        <td class="value-cell">{fax}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">SALES:</td>
                        <td class="value-cell">{data.get('sales_person', 'SALES TEAM')}</td>
                        <td class="label-cell"></td>
                        <td class="value-cell"></td>
                    </tr>
                </table>
            </div>
            
            <div class="document-title">{document_type}</div>
            
            <div class="contract-info">
                <div class="contract-number">Contract No: {data.get('contract_no', 'HB-ENG-2025-001')}</div>
                <div class="contract-description">{data.get('contract_description', 'ELECTRICAL INSTALLATION AND MAINTENANCE CONTRACT')}</div>
            </div>
            """
        
        # 項目列表HTML
        items_html = ""
        for i, item in enumerate(page_items):
            # 計算全局項目編號
            if page_num == 1:
                item_number = i + 1
            else:
                item_number = 5 + (page_num - 2) * 6 + i + 1
            
            # 計算金額
            quantity = item.get('quantity', 1)
            unit_price = item.get('unit_price', 0)
            discount = item.get('discount', 0)
            amount = quantity * unit_price * (1 - discount / 100)
            
            items_html += f"""
            <div class="accessory-item">
                <div class="item-header">[{item_number}] ACCESSORY :</div>
                <div class="item-content">
                    <div class="item-description">
                        <div class="model-line">Model: {item.get('model', item.get('product_name', 'N/A'))}</div>
                        <div class="description-text">{item.get('description', '')}</div>
                    </div>
                    <div class="item-pricing">
                        <table class="pricing-table">
                            <thead>
                                <tr>
                                    <th>SET (S)</th>
                                    <th>HKD</th>
                                    <th></th>
                                    <th>HKD</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="quantity-cell">{quantity:.1f}</td>
                                    <td class="price-cell">{unit_price:,.2f}</td>
                                    <td class="empty-cell"></td>
                                    <td class="amount-cell">{amount:,.2f}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            """
        
        # 總計和條款HTML（僅最後一頁顯示）
        summary_html = ""
        if page_num == total_pages:
            # 備註
            remarks = data.get('remarks', data.get('notes', ''))
            remarks_html = ""
            if remarks:
                remarks_html = f"""
            <div class="remarks-section">
                <div class="remarks-title">REMARKS:</div>
                <div class="remarks-content">{remarks}</div>
            </div>
                """
            
            summary_html = f"""
            <div class="total-section">
                <div class="total-amount">
                    <span class="total-label">TOTAL HKD</span>
                    <span class="total-value">{data.get('total_amount', 0):,.2f}</span>
                </div>
            </div>
            
            {remarks_html}
            
            <div class="terms-section">
                <div class="terms-item"><strong>PRICING:</strong> {data.get('pricing_terms', 'C.I.F. HONG KONG')}</div>
                <div class="terms-item"><strong>VALIDITY:</strong> THIS QUOTATION IS VALID TILL {data.get('valid_until', 'MAY 17, 25')}</div>
                <div class="terms-item"><strong>DELIVERY:</strong> {data.get('delivery_terms', 'APPROX. 4 WEEKS')}</div>
                <div class="terms-item"><strong>PAYMENT:</strong> {data.get('terms', '30% DEPOSIT, 40% C.O.D., 30% AFTER T & C')}</div>
                <div class="terms-item"><strong>REMARK:</strong> THE ABOVE PRICES ARE OFFERS AFTER DISCOUNT</div>
            </div>
            
            <div class="acceptance-section">
                <div class="acceptance-checkbox">
                    <span class="checkbox">☐</span> THE ABOVE QUOTATION IS ACCEPTED BY THE UNDERSIGNED BUYER
                </div>
            </div>
            
            <div class="signature-section">
                <div class="signature-box buyer-signature">
                    <div class="signature-line"></div>
                    <div class="signature-label">BUYER</div>
                    <div class="signature-company">{data.get('customer_name', '')}</div>
                </div>
                <div class="signature-box seller-signature">
                    <div class="signature-line"></div>
                    <div class="signature-label">SELLER</div>
                    <div class="signature-company">{company_name_en}</div>
                </div>
            </div>
            """
        
        # 頁尾HTML
        footer_html = f"""
        <div class="page-footer">
            <div class="footer-left">{data.get('customer_name', '')}</div>
            <div class="footer-center">PAGE {page_num} OF {total_pages}</div>
            <div class="footer-right">{company_name_en}</div>
            <div class="print-date">PRINT DATE: {datetime.now().strftime('%B %d, %Y').upper()}</div>
        </div>
        """
        
        # 組合完整頁面
        page_class = "page" if page_num < total_pages else "page last-page"
        page_html = f"""
        <div class="{page_class}">
            {header_html}
            {document_info_html}
            <div class="items-section">
                {items_html}
            </div>
            {summary_html}
            {footer_html}
        </div>
        """
        
        return page_html
    
    def _get_professional_css(self) -> str:
        """獲取專業的CSS樣式"""
        return """
        @page {
            size: A4;
            margin: 15mm;
            @bottom-center {
                content: "";
            }
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 10pt;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
        }
        
        .page {
            page-break-after: always;
            position: relative;
            min-height: 100vh;
        }
        
        .last-page {
            page-break-after: avoid;
        }
        
        /* 頁首樣式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12pt;
            border-bottom: 1pt solid #000;
            padding-bottom: 6pt;
        }
        
        .company-info-left {
            flex: 1;
            line-height: 1.1;
        }
        
        .company-name-cn {
            font-size: 12pt;
            font-weight: bold;
            margin-bottom: 2pt;
        }
        
        .company-name-en {
            font-size: 10pt;
            font-weight: bold;
            margin-bottom: 1pt;
        }
        
        .company-nature {
            font-size: 8pt;
            color: #333;
            font-style: italic;
        }
        
        .customer-info-right {
            flex: 1;
            text-align: right;
            line-height: 1.1;
        }
        
        .customer-info-title {
            font-size: 9pt;
            font-weight: bold;
            margin-bottom: 4pt;
            text-decoration: underline;
        }
        
        .customer-details {
            font-size: 8pt;
            line-height: 1.2;
        }
        
        .customer-name {
            font-weight: bold;
            margin-bottom: 2pt;
        }
        
        .contact-person {
            margin-bottom: 2pt;
        }
        
        .customer-address {
            margin-bottom: 2pt;
        }
        
        /* 文檔信息樣式 */
        .document-metadata {
            margin-bottom: 10pt;
        }
        
        .metadata-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10pt;
        }
        
        .metadata-table td {
            padding: 2pt 6pt;
            border: 1pt solid #000;
            font-size: 8pt;
            height: 14pt;
        }
        
        .label-cell {
            background-color: #f5f5f5;
            font-weight: bold;
            width: 12%;
        }
        
        .value-cell {
            width: 38%;
        }
        
        .document-title {
            text-align: center;
            font-size: 20pt;
            font-weight: bold;
            margin: 12pt 0;
            letter-spacing: 2pt;
        }
        
        .contract-info {
            margin-bottom: 12pt;
            text-align: left;
            line-height: 1.1;
        }
        
        .contract-number {
            font-size: 8pt;
            margin-bottom: 2pt;
        }
        
        .contract-description {
            font-size: 8pt;
            font-weight: bold;
        }
        
        /* 項目樣式 */
        .items-section {
            margin-bottom: 12pt;
        }
        
        .accessory-item {
            margin-bottom: 10pt;
            border: 1pt solid #000;
            padding: 6pt;
            background-color: white;
        }
        
        .item-header {
            font-size: 9pt;
            font-weight: bold;
            margin-bottom: 4pt;
            color: #000;
        }
        
        .item-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .item-description {
            flex: 2.5;
            margin-right: 12pt;
        }
        
        .model-line {
            font-size: 8pt;
            font-weight: bold;
            margin-bottom: 3pt;
            color: #000;
        }
        
        .description-text {
            font-size: 7pt;
            line-height: 1.3;
            color: #000;
        }
        
        .item-pricing {
            flex: 1;
            min-width: 120pt;
        }
        
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 7pt;
        }
        
        .pricing-table th,
        .pricing-table td {
            border: 1pt solid #000;
            padding: 2pt;
            text-align: center;
            height: 12pt;
        }
        
        .pricing-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 6pt;
        }
        
        .price-cell,
        .amount-cell {
            text-align: right;
            padding-right: 4pt;
        }
        
        .quantity-cell {
            text-align: center;
        }
        
        .empty-cell {
            background-color: #f9f9f9;
        }
        
        /* 總計樣式 */
        .total-section {
            margin: 12pt 0;
            text-align: right;
        }
        
        .total-amount {
            display: inline-block;
            border: 2pt solid #000;
            padding: 6pt 12pt;
            background-color: white;
        }
        
        .total-label {
            font-size: 10pt;
            font-weight: bold;
            margin-right: 12pt;
        }
        
        .total-value {
            font-size: 12pt;
            font-weight: bold;
        }
        
        /* 備註樣式 */
        .remarks-section {
            margin: 8pt 0;
            font-size: 8pt;
        }
        
        .remarks-title {
            font-weight: bold;
            margin-bottom: 3pt;
        }
        
        .remarks-content {
            line-height: 1.3;
        }
        
        /* 條款樣式 */
        .terms-section {
            margin: 12pt 0;
            font-size: 8pt;
            line-height: 1.3;
        }
        
        .terms-item {
            margin-bottom: 3pt;
        }
        
        /* 接受區域樣式 */
        .acceptance-section {
            margin: 12pt 0;
        }
        
        .acceptance-checkbox {
            font-size: 8pt;
            display: flex;
            align-items: center;
        }
        
        .checkbox {
            margin-right: 6pt;
            font-size: 10pt;
        }
        
        /* 簽名區域樣式 */
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin: 20pt 0 12pt 0;
        }
        
        .signature-box {
            width: 45%;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 1pt solid #000;
            height: 20pt;
            margin-bottom: 4pt;
        }
        
        .signature-label {
            font-size: 9pt;
            font-weight: bold;
            margin-bottom: 2pt;
        }
        
        .signature-company {
            font-size: 7pt;
        }
        
        /* 頁尾樣式 */
        .page-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 7pt;
            border-top: 1pt solid #000;
            padding-top: 4pt;
        }
        
        .footer-left,
        .footer-right {
            font-size: 6pt;
        }
        
        .footer-center {
            font-weight: bold;
            font-size: 7pt;
        }
        
        .print-date {
            position: absolute;
            bottom: -10pt;
            right: 0;
            font-size: 5pt;
            color: #666;
        }
        """ 