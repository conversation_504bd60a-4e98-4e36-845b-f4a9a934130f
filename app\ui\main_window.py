from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QToolBar, QMessageBox, QFrame, QSplitter, QStatusBar, QMenu, QAction
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QPixmap, QFont
from app.database.db_manager import DatabaseManager
from app.ui.customer_manager import CustomerManager
from app.ui.system_settings import SystemSettings
from app.ui.product_manager import ProductManager
from app.ui.quotation_manager import QuotationManager
from app.ui.invoice_manager import InvoiceManager
from app.ui.delivery_note_manager import DeliveryNoteManager
from app.ui.recycle_bin_manager import RecycleBinManager
from app.ui.user_management_dialog import UserManagementDialog
from app.ui.backup_dialog import BackupDialog
from app.ui.restore_dialog import RestoreDialog
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager
from app.utils.permission_manager import PermissionManager
from app.utils.logger import system_logger
import os
import sys

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 初始化語言管理器
        self.lang_manager = LanguageManager()
        # 初始化圖標管理器
        self.icon_manager = IconManager()
        # 初始化數據庫管理器
        self.db_manager = DatabaseManager()
        # 用戶狀態
        self.current_user = None
        
        # 加載樣式表
        self.load_stylesheet()
        
        # 初始化界面 (先建立UI元素)
        self.init_ui()
        
        # 顯示登錄對話框 (UI元素建立後再顯示登入)
        self.show_login_dialog()
        
        # 更新界面權限設置
        self.update_ui_permissions()
        
    def load_stylesheet(self):
        """加載樣式表"""
        style_path = "app/assets/styles.qss"
        if os.path.exists(style_path):
            with open(style_path, "r", encoding="utf-8") as f:
                self.setStyleSheet(f.read())
        
    def init_ui(self):
        # 設置視窗標題和大小
        self.setWindowTitle(self.lang_manager.get_text('app_title'))
        self.setGeometry(100, 100, 1200, 800)
        
        # 創建空工具欄 (無需添加語言按鈕，因為稍後會添加到狀態欄)
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 使用分割器實現可調整寬度的布局
        splitter = QSplitter(Qt.Horizontal)
        
        # 創建左側導航面板
        nav_panel = QFrame()
        nav_panel.setFrameShape(QFrame.StyledPanel)
        nav_panel.setMaximumWidth(220)
        nav_panel.setMinimumWidth(180)
        
        # 創建導航面板布局
        nav_layout = QVBoxLayout(nav_panel)
        nav_layout.setContentsMargins(5, 10, 5, 10)
        nav_layout.setSpacing(8)
        
        # 添加公司Logo
        logo_label = QLabel()
        logo_path = "app/assets/images/logo.png"
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(180, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            logo_label.setText("蒼藍工程公司")
            logo_label.setFont(QFont("Arial", 14, QFont.Bold))
        logo_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(logo_label)
        
        # 添加分隔線
        self.add_separator(nav_layout)
        
        # 添加菜單標題
        self.add_group_header(nav_layout, self.lang_manager.get_text('menu_title'), "menu")
        
        # ===== 系統功能組 =====
        self.add_group_header(nav_layout, self.lang_manager.get_text('system_functions'), "system")
        
        # 數據管理下拉菜單按鈕
        self.btn_data_management = self.create_menu_button(
            self.lang_manager.get_text('data_management'),
            'database',
            [
                ('backup', self.lang_manager.get_text('backup_function'), self.show_backup_dialog),
                ('restore', self.lang_manager.get_text('restore_function'), self.show_restore_dialog),
                ('recycle', '回收站管理', self.show_recycle_bin_manager)
            ]
        )
        
        # 添加系統功能按鈕
        nav_layout.addWidget(self.btn_data_management)
        
        self.add_separator(nav_layout)
        
        # ===== 文件管理組 =====
        self.add_group_header(nav_layout, self.lang_manager.get_text('document_management'), "document")
        
        # 文件管理下拉菜單按鈕
        self.btn_document_management = self.create_menu_button(
            self.lang_manager.get_text('doc_management'),
            'document',
            [
                ('quotation', self.lang_manager.get_text('quotation'), self.show_quotation_manager),
                ('invoice', self.lang_manager.get_text('invoice'), self.show_invoice_manager),
                ('delivery', self.lang_manager.get_text('delivery_note'), self.show_delivery_note_manager)
            ]
        )
        
        # 添加文件管理按鈕
        nav_layout.addWidget(self.btn_document_management)
        
        self.add_separator(nav_layout)
        
        # ===== 基礎數據組 =====
        self.add_group_header(nav_layout, self.lang_manager.get_text('basic_data_management'), "data")
        
        # 基礎數據下拉菜單按鈕
        self.btn_basic_data = self.create_menu_button(
            self.lang_manager.get_text('basic_data'),
            'data',
            [
                ('customer', self.lang_manager.get_text('customer_management'), self.show_customer_manager),
                ('product', self.lang_manager.get_text('product_management'), self.show_product_manager)
            ]
        )
        
        # 添加基礎數據按鈕
        nav_layout.addWidget(self.btn_basic_data)
        
        self.add_separator(nav_layout)
        
        # ===== 系統設置組 =====
        self.add_group_header(nav_layout, self.lang_manager.get_text('system_settings_group'), "settings")
        
        # 用戶管理按鈕
        self.btn_user = self.create_button(
            self.lang_manager.get_text('user_management'),
            'user',
            self.show_user_management
        )
        
        # 系統設定按鈕
        self.btn_settings = self.create_button(
            self.lang_manager.get_text('system_settings'),
            'settings',
            self.show_system_settings
        )
        
        # 添加系統設置按鈕
        nav_layout.addWidget(self.btn_user)
        nav_layout.addWidget(self.btn_settings)
        
        nav_layout.addStretch()
        
        # 添加版權信息
        copyright_label = QLabel("© 2024 蒼藍工程公司")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("color: #888888; font-size: 9pt;")
        nav_layout.addWidget(copyright_label)
        
        # 創建右側內容面板
        content_panel = QFrame()
        content_panel.setFrameShape(QFrame.StyledPanel)
        
        # 創建內容區域布局
        self.content_layout = QVBoxLayout(content_panel)
        
        # 添加歡迎信息
        self.welcome_label = QLabel(self.lang_manager.get_text('welcome'))
        self.welcome_label.setAlignment(Qt.AlignCenter)
        self.welcome_label.setObjectName("welcome-label")
        self.content_layout.addWidget(self.welcome_label)
        
        # 添加分割器
        splitter.addWidget(nav_panel)
        splitter.addWidget(content_panel)
        
        # 設置分割比例
        splitter.setSizes([220, 980])
        
        # 創建主布局並添加分割器
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(splitter)
        
        # 添加狀態欄
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage(self.lang_manager.get_text('welcome'))
        
        # 創建登出按鈕並添加到狀態欄
        self.btn_logout = QPushButton(self.lang_manager.get_text('logout'))
        self.btn_logout.setIcon(self.icon_manager.get_icon('logout'))
        self.btn_logout.setFixedWidth(120)
        self.btn_logout.clicked.connect(self.logout)
        self.btn_logout.setStyleSheet("""
            QPushButton {
                background-color: #f44336; 
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        
        # 在狀態欄右側添加語言切換按鈕
        self.lang_btn = QPushButton(self.lang_manager.get_text('switch_language'))
        self.lang_btn.setIcon(self.icon_manager.get_icon('language'))
        self.lang_btn.setFixedWidth(150)  # 設置固定寬度，確保文字不被截斷
        self.lang_btn.setStyleSheet("QPushButton { text-align: left; padding-left: 10px; background-color: #4a86e8; color: white; border-radius: 4px; }")
        self.lang_btn.clicked.connect(self.switch_language)
        
        # 添加按鈕到狀態欄（從右往左順序排列）
        self.status_bar.addPermanentWidget(self.lang_btn)   # 最右邊
        self.status_bar.addPermanentWidget(self.btn_logout) # 語言按鈕左側
    
    def create_button(self, text, icon_name, callback):
        """創建一個標準按鈕"""
        btn = QPushButton(text)
        btn.setIcon(self.icon_manager.get_icon(icon_name))
        btn.setIconSize(QSize(22, 22))
        
        # 設置樣式
        btn.setProperty("class", "nav-button")
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f0ff;
            }
            QPushButton:pressed {
                background-color: #d0e0ff;
            }
        """)
        
        btn.clicked.connect(callback)
        return btn
    
    def create_menu_button(self, text, icon_name, actions):
        """創建一個帶有下拉菜單的按鈕"""
        btn = QPushButton(text + "  ▾")  # 添加下拉箭頭
        btn.setIcon(self.icon_manager.get_icon(icon_name))
        btn.setIconSize(QSize(22, 22))
        
        # 設置樣式
        btn.setProperty("class", "nav-menu-button")
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e6f0ff;
            }
            QPushButton:pressed {
                background-color: #d0e0ff;
            }
        """)
        
        # 創建菜單
        menu = QMenu(self)
        for action_icon, action_text, action_callback in actions:
            action = QAction(self.icon_manager.get_icon(action_icon), action_text, self)
            action.triggered.connect(action_callback)
            menu.addAction(action)
        
        btn.setMenu(menu)
        return btn
    
    def add_separator(self, layout):
        """添加分隔線"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #cccccc; margin: 5px 0;")
        separator.setMaximumHeight(1)
        layout.addWidget(separator)
    
    def add_group_header(self, layout, text, icon_name=None):
        """添加分組標籤"""
        header = QLabel(text)
        header.setStyleSheet("""
            font-weight: bold; 
            color: #333333; 
            padding: 5px 8px;
            background-color: #e6e6e6;
            border-left: 4px solid #4a86e8;
            border-radius: 2px;
        """)
        
        if icon_name:
            header.setIndent(5)
        
        layout.addWidget(header)
    
    def show_message(self, message):
        """顯示提示信息（用於尚未實現的功能）"""
        self.clear_content()
        msg_label = QLabel(message)
        msg_label.setAlignment(Qt.AlignCenter)
        self.content_layout.addWidget(msg_label)
        
    def show_customer_manager(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('customer_management'))
        # 每次都創建新的客戶管理界面，並傳入權限管理器
        customer_manager = CustomerManager(self.db_manager, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(customer_manager)
        
    def show_product_manager(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('product_management'))
        # 每次都創建新的產品管理界面，並傳入權限管理器
        product_manager = ProductManager(self.db_manager, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(product_manager)
        
    def show_quotation_manager(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('quotation'))
        # 每次都創建新的報價單管理界面，並傳入 self 作為 main_window 參數和權限管理器
        quotation_manager = QuotationManager(self.db_manager, main_window=self, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(quotation_manager)
        
    def show_invoice_manager(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('invoice'))
        # 每次都創建新的發票管理界面，並傳入 self 作為 main_window 參數和權限管理器
        invoice_manager = InvoiceManager(self.db_manager, main_window=self, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(invoice_manager)
        
    def show_delivery_note_manager(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('delivery_note'))
        # 每次都創建新的送貨單管理界面，並傳入 self 作為 main_window 參數和權限管理器
        delivery_note_manager = DeliveryNoteManager(self.db_manager, main_window=self, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(delivery_note_manager)
    
    def show_recycle_bin_manager(self):
        """顯示回收站管理"""
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage("回收站管理")
        # 創建回收站管理界面
        recycle_bin_manager = RecycleBinManager(self.db_manager, main_window=self, permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(recycle_bin_manager)
        
    def show_system_settings(self):
        self.clear_content()
        # 更新狀態欄
        self.status_bar.showMessage(self.lang_manager.get_text('system_settings'))
        # 每次都創建新的系統設定界面，並傳入權限管理器
        system_settings = SystemSettings(permission_mgr=self.permission_mgr)
        self.content_layout.addWidget(system_settings)
        
    def show_user_management(self):
        """顯示用戶管理對話框"""
        # 使用權限管理器檢查用戶權限
        if not self.permission_mgr.check_user_management_permission():
            return
            
        # 如果有權限，則創建用戶管理對話框
        try:
            user_dialog = UserManagementDialog(self, self.db_manager, self.current_user)
            if user_dialog.exec_() == UserManagementDialog.Accepted:
                pass  # 如果需要處理對話框返回的結果
        except ImportError as e:
            system_logger.error(f"無法導入用戶管理對話框模塊: {str(e)}")
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"無法載入用戶管理功能\n{str(e)}"
            )
        except Exception as e:
            system_logger.error(f"顯示用戶管理對話框時發生錯誤: {str(e)}")
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('operation_failed')}\n{str(e)}"
            )
        
    def show_backup_dialog(self):
        """顯示備份對話框"""
        # 使用權限管理器檢查用戶權限
        if not self.permission_mgr.check_user_management_permission():
            return
            
        # 如果有權限，則創建備份對話框
        try:
            backup_dialog = BackupDialog(self, self.db_manager)
            backup_dialog.exec_()
        except Exception as e:
            system_logger.error(f"顯示備份對話框時發生錯誤: {str(e)}")
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('operation_failed')}\n{str(e)}"
            )
        
    def show_restore_dialog(self):
        """顯示還原對話框"""
        # 使用權限管理器檢查用戶權限
        if not self.permission_mgr.check_user_management_permission():
            return
            
        # 如果有權限，則創建還原對話框
        try:
            restore_dialog = RestoreDialog(self, self.db_manager)
            restore_dialog.exec_()
        except Exception as e:
            system_logger.error(f"顯示還原對話框時發生錯誤: {str(e)}")
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('operation_failed')}\n{str(e)}"
            )
        
    def clear_content(self):
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
                
    def switch_language(self):
        """切換語言並刷新界面"""
        try:
            # 切換語言
            self.lang_manager.switch_language()
            
            # 更新界面文字
            self.setWindowTitle(self.lang_manager.get_text('app_title'))
            
            # 更新語言按鈕文字
            if hasattr(self, 'lang_btn') and self.lang_btn:
                self.lang_btn.setText(self.lang_manager.get_text('switch_language'))
            
            # 更新導航按鈕文字 - 更新為新的合併按鈕
            if hasattr(self, 'btn_document_management'):
                self.btn_document_management.setText(self.lang_manager.get_text('doc_management') + "  ▾")
            if hasattr(self, 'btn_basic_data'):
                self.btn_basic_data.setText(self.lang_manager.get_text('basic_data') + "  ▾")
            if hasattr(self, 'btn_user'):
                self.btn_user.setText(self.lang_manager.get_text('user_management'))
            if hasattr(self, 'btn_settings'):
                self.btn_settings.setText(self.lang_manager.get_text('system_settings'))
            if hasattr(self, 'btn_data_management'):
                self.btn_data_management.setText(self.lang_manager.get_text('data_management') + "  ▾")
            if hasattr(self, 'btn_logout'):
                self.btn_logout.setText(self.lang_manager.get_text('logout'))
                
            # 更新分組標籤
            for child in self.findChildren(QLabel):
                if child.text() == self.lang_manager.get_text('system_functions', 'en' if self.lang_manager.current_language == 'zh_HK' else 'zh_HK'):
                    child.setText(self.lang_manager.get_text('system_functions'))
                elif child.text() == self.lang_manager.get_text('document_management', 'en' if self.lang_manager.current_language == 'zh_HK' else 'zh_HK'):
                    child.setText(self.lang_manager.get_text('document_management'))
                elif child.text() == self.lang_manager.get_text('basic_data_management', 'en' if self.lang_manager.current_language == 'zh_HK' else 'zh_HK'):
                    child.setText(self.lang_manager.get_text('basic_data_management'))
                elif child.text() == self.lang_manager.get_text('system_settings_group', 'en' if self.lang_manager.current_language == 'zh_HK' else 'zh_HK'):
                    child.setText(self.lang_manager.get_text('system_settings_group'))
            
            # 安全地更新歡迎標籤
            try:
                if hasattr(self, 'welcome_label') and self.welcome_label:
                    self.welcome_label.setText(self.lang_manager.get_text('welcome'))
            except Exception as e:
                system_logger.error(f"更新歡迎標籤時發生錯誤: {str(e)}")
            
            # 更新子模塊翻譯（只有當前正在顯示的界面需要更新）
            if hasattr(self, 'content_layout'):
                current_content = None
                for i in range(self.content_layout.count()):
                    widget = self.content_layout.itemAt(i).widget()
                    if widget:
                        current_content = widget
                        break
                        
                if current_content:
                    if hasattr(current_content, 'refresh_translations'):
                        try:
                            current_content.refresh_translations()
                        except Exception as e:
                            system_logger.error(f"更新模塊翻譯時發生錯誤: {str(e)}")
            
            # 更新狀態欄
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.showMessage(self.lang_manager.get_text('welcome'))
            
            # 通知用戶語言已切換
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('tip'),
                '語言已切換！' if self.lang_manager.current_language == 'zh_HK' else 'Language switched!'
            )
        except Exception as e:
            system_logger.error(f"切換語言時發生錯誤: {str(e)}")

    def show_login_dialog(self):
        """顯示登錄對話框"""
        try:
            from app.ui.login_dialog import LoginDialog
            
            # 確保數據庫已初始化，改進連接邏輯
            if self.db_manager:
                try:
                    system_logger.info("嘗試連接資料庫...")
                    # 明確檢查連接是否成功
                    if self.db_manager.connect():
                        system_logger.info("資料庫連接成功，初始化資料庫...")
                        
                        # 初始化資料庫，確保表存在
                        if self.db_manager.init_database():
                            system_logger.info("資料庫初始化成功")
                            
                            # 確保 cursor 存在才執行查詢
                            if self.db_manager.cursor:
                                # 檢查admin用戶是否存在，若不存在則創建
                                self.db_manager.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
                                admin_count = self.db_manager.cursor.fetchone()[0]
                                system_logger.info(f"找到 {admin_count} 個 admin 用戶")
                                
                                if admin_count == 0:
                                    system_logger.info("創建 admin 用戶...")
                                    import hashlib
                                    password_hash = hashlib.sha256('admin'.encode()).hexdigest()
                                    self.db_manager.cursor.execute("""
                                        INSERT INTO users (username, password_hash, full_name, role, status)
                                        VALUES (?, ?, ?, ?, ?)
                                    """, ('admin', password_hash, '系統管理員', 'admin', 'active'))
                                    self.db_manager.conn.commit()
                                    system_logger.info("admin 用戶創建成功")
                            else:
                                system_logger.error("錯誤: 資料庫游標為空")
                        else:
                            system_logger.error("錯誤: 資料庫初始化失敗")
                    else:
                        system_logger.error("錯誤: 資料庫連接失敗")
                    
                    # 確保斷開連接
                    if self.db_manager.conn:
                        self.db_manager.disconnect()
                        system_logger.info("資料庫連接已關閉")
                        
                    # 打印提示信息
                    system_logger.info("請使用以下憑據登入：")
                    system_logger.info("用戶名: admin")
                    system_logger.info("密碼: admin")
                    
                except Exception as e:
                    system_logger.error(f"資料庫操作錯誤: {str(e)}")
                    # 確保出錯時也斷開連接
                    if hasattr(self.db_manager, 'conn') and self.db_manager.conn:
                        self.db_manager.disconnect()
                        system_logger.info("資料庫連接已關閉（錯誤處理）")
            
            system_logger.info("創建登入對話框...")
            login_dialog = LoginDialog(self, self.db_manager)
            login_dialog.login_success.connect(self.on_login_success)
            
            system_logger.info("顯示登入對話框...")
            if login_dialog.exec_() != LoginDialog.Accepted:
                # 如果用戶關閉登錄對話框而沒有登錄，則退出應用程序
                system_logger.info("用戶取消登入，退出應用程式")
                sys.exit()
                
            # 登入成功後，更新界面權限
            self.update_ui_permissions()
        except Exception as e:
            system_logger.error(f"顯示登入對話框時發生錯誤: {str(e)}")
            # 如果無法顯示登入對話框，則退出應用
            sys.exit(1)
    
    def on_login_success(self, user_data):
        """登錄成功處理"""
        try:
            self.current_user = user_data
            # 更新狀態欄顯示當前用戶
            if hasattr(self, 'status_bar') and self.status_bar is not None:
                welcome_message = f"{self.lang_manager.get_text('welcome')} {self.current_user.get('username', '')}"
                self.status_bar.showMessage(welcome_message)
                system_logger.info(f"成功設置歡迎訊息: {welcome_message}")
            else:
                system_logger.warning("警告: status_bar 未初始化，無法設置歡迎訊息")
                
            # 更新界面權限設置
            self.update_ui_permissions()
        except Exception as e:
            system_logger.error(f"設置歡迎訊息時發生錯誤: {str(e)}")

    def update_ui_permissions(self):
        """更新界面權限設置"""
        # 檢查用戶角色
        is_admin = self.current_user and self.current_user.get('role') == 'admin'
        is_manager = self.current_user and self.current_user.get('role') == 'manager'
        
        # 設置用戶管理按鈕的權限 - 僅管理員可用
        if hasattr(self, 'btn_user'):
            # 只有管理員可以使用用戶管理功能
            self.btn_user.setEnabled(is_admin)
            if not is_admin:
                self.btn_user.setToolTip(self.lang_manager.get_text('admin_only_feature'))
                # 將按鈕設置為灰色
                self.btn_user.setStyleSheet("""
                    QPushButton { 
                        color: #aaaaaa; 
                        background-color: #f0f0f0; 
                        text-align: left;
                        padding: 8px 12px;
                        border-radius: 4px;
                    }
                """)
            else:
                self.btn_user.setToolTip(self.lang_manager.get_text('user_management'))
                self.btn_user.setStyleSheet("""
                    QPushButton {
                        text-align: left;
                        padding: 8px 12px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #e6f0ff;
                    }
                    QPushButton:pressed {
                        background-color: #d0e0ff;
                    }
                """)
        
        # 設置數據管理按鈕的權限 - 僅管理員可用
        if hasattr(self, 'btn_data_management'):
            # 只有管理員可以使用數據管理功能
            self.btn_data_management.setEnabled(is_admin)
            if not is_admin:
                self.btn_data_management.setToolTip(self.lang_manager.get_text('admin_only_feature'))
                # 將按鈕設置為灰色
                self.btn_data_management.setStyleSheet("""
                    QPushButton { 
                        color: #aaaaaa; 
                        background-color: #f0f0f0; 
                        text-align: left;
                        padding: 8px 12px;
                        border-radius: 4px;
                    }
                """)
            else:
                self.btn_data_management.setToolTip(self.lang_manager.get_text('data_management'))
                self.btn_data_management.setStyleSheet("""
                    QPushButton {
                        text-align: left;
                        padding: 8px 12px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #e6f0ff;
                    }
                    QPushButton:pressed {
                        background-color: #d0e0ff;
                    }
                """)
                
        # 設置其他功能按鈕的權限
        # 一般用戶只能查看，高級用戶和管理員可以修改
        self.can_modify = is_admin or is_manager
        self.can_view_only = not self.can_modify
        
        # 將權限信息保存到全局變量，以便在各個功能模塊中使用
        self.user_role = self.current_user.get('role') if self.current_user else None
        
        # 創建權限管理器
        self.permission_mgr = PermissionManager(self, self.user_role)
        
        # 更新按鈕提示信息
        buttons = [self.btn_document_management, self.btn_basic_data]
        
        for btn in buttons:
            if self.can_view_only:
                # 一般用戶僅查看提示
                current_text = btn.text().replace("  ▾", "")  # 移除下拉箭頭以獲取純文本
                btn.setToolTip(f"{current_text} ({self.lang_manager.get_text('view_only')})")
            else:
                # 移除僅查看提示
                current_text = btn.text().replace("  ▾", "")
                btn.setToolTip(current_text)

    def logout(self):
        """登出並返回登入界面"""
        # 確認是否要登出
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('logout_confirm'),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重置用戶狀態
            self.current_user = None
            
            # 清空內容
            self.clear_content()
            
            # 顯示歡迎界面
            self.welcome_label = QLabel(self.lang_manager.get_text('welcome'))
            self.welcome_label.setAlignment(Qt.AlignCenter)
            self.welcome_label.setObjectName("welcome-label")
            self.content_layout.addWidget(self.welcome_label)
            
            # 關閉當前窗口
            self.hide()
            
            # 顯示登入對話框
            self.show_login_dialog()
            
            # 重新顯示窗口
            self.show()
            
            # 更新界面權限
            self.update_ui_permissions() 