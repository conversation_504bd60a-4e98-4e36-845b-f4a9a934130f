#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_weasyprint_installation():
    """測試WeasyPrint是否正確安裝"""
    try:
        import weasyprint
        print("✅ WeasyPrint已安裝")
        print(f"   版本: {weasyprint.__version__}")
        return True
    except ImportError as e:
        print(f"❌ WeasyPrint未安裝: {e}")
        print("   請運行: pip install weasyprint")
        return False

def test_weasyprint_generator():
    """測試WeasyPrint PDF生成器"""
    try:
        from app.utils.pdf_generator_weasy import PDFGeneratorWeasy
        print("✅ 成功導入WeasyPrint PDF生成器")
        
        # 創建生成器實例
        pdf_generator = PDFGeneratorWeasy()
        print("✅ PDF生成器創建成功")
        
        # 測試數據
        test_quotation_data = {
            'quotation_no': 'WEASY-TEST-001',
            'date': 'MAY 28, 25',
            'valid_until': 'JUNE 28, 25',
            'customer_name': 'WEASYPRINT TEST COMPANY LIMITED',
            'contact_person': 'MR. WEASY PRINT TESTER',
            'address': 'WeasyPrint Test Address, Professional Building, Test District',
            'phone': '+852 2345 6789',
            'fax': '+852 2345 6790',
            'sales_person': 'WEASY SALES',
            'contract_no': 'HB-ENG-2025-WEASY',
            'contract_description': 'WEASYPRINT PDF GENERATION TEST CONTRACT',
            'items': [
                {
                    'item_no': 1,
                    'model': 'WEASY-MODEL-001',
                    'description': 'WeasyPrint test item with professional PDF generation capabilities, including advanced CSS styling and precise layout control',
                    'quantity': 2.0,
                    'unit': 'SET',
                    'unit_price': 5000.00,
                    'discount': 0,
                    'amount': 10000.00
                },
                {
                    'item_no': 2,
                    'model': 'WEASY-MODEL-002',
                    'description': 'Advanced PDF formatting test item with complex layout requirements and professional styling',
                    'quantity': 1.0,
                    'unit': 'SYSTEM',
                    'unit_price': 15000.00,
                    'discount': 5,
                    'amount': 14250.00
                },
                {
                    'item_no': 3,
                    'model': 'WEASY-MODEL-003',
                    'description': 'Multi-page test item to verify pagination and layout consistency across pages',
                    'quantity': 3.0,
                    'unit': 'SET',
                    'unit_price': 3000.00,
                    'discount': 0,
                    'amount': 9000.00
                }
            ],
            'total_amount': 33250.00,
            'terms': '30% DEPOSIT, 40% C.O.D., 30% AFTER T & C',
            'delivery_terms': 'APPROX. 6 WEEKS',
            'pricing_terms': 'C.I.F. HONG KONG',
            'remarks': 'This is a WeasyPrint test with professional PDF generation capabilities.'
        }
        
        print("🔄 正在生成WeasyPrint PDF...")
        print(f"📊 項目數量: {len(test_quotation_data['items'])}")
        print(f"💰 總金額: HK$ {test_quotation_data['total_amount']:,.2f}")
        
        # 生成報價單PDF
        quotation_pdf = pdf_generator.generate_quotation_pdf(test_quotation_data)
        
        if quotation_pdf and os.path.exists(quotation_pdf):
            file_size = os.path.getsize(quotation_pdf)
            print(f"✅ 報價單PDF生成成功: {quotation_pdf}")
            print(f"📄 文件大小: {file_size:,} bytes")
        else:
            print("❌ 報價單PDF生成失敗")
            return False
        
        # 生成發票PDF
        invoice_data = test_quotation_data.copy()
        invoice_data['invoice_no'] = 'WEASY-INV-001'
        invoice_pdf = pdf_generator.generate_invoice_pdf(invoice_data)
        
        if invoice_pdf and os.path.exists(invoice_pdf):
            print(f"✅ 發票PDF生成成功: {invoice_pdf}")
        else:
            print("❌ 發票PDF生成失敗")
            return False
        
        # 生成送貨單PDF
        delivery_data = test_quotation_data.copy()
        delivery_data['delivery_note_no'] = 'WEASY-DN-001'
        delivery_pdf = pdf_generator.generate_delivery_note_pdf(delivery_data)
        
        if delivery_pdf and os.path.exists(delivery_pdf):
            print(f"✅ 送貨單PDF生成成功: {delivery_pdf}")
        else:
            print("❌ 送貨單PDF生成失敗")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_manager():
    """測試PDF管理器"""
    try:
        from app.utils.pdf_manager import PDFManager
        print("\n🔄 測試PDF管理器...")
        
        # 創建PDF管理器
        pdf_manager = PDFManager()
        print("✅ PDF管理器創建成功")
        
        # 獲取生成器信息
        info = pdf_manager.get_generator_info()
        print(f"📋 當前生成器: {info['type']}")
        print(f"   類別: {info['class']}")
        print(f"   可用: {info['available']}")
        
        # 測試生成功能
        test_result = pdf_manager.test_generation()
        if test_result:
            print("✅ PDF管理器測試通過")
            return True
        else:
            print("❌ PDF管理器測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ PDF管理器測試失敗: {e}")
        return False

def test_multi_page_generation():
    """測試多頁面PDF生成"""
    try:
        from app.utils.pdf_generator_weasy import PDFGeneratorWeasy
        pdf_generator = PDFGeneratorWeasy()
        
        print("\n🔄 測試多頁面PDF生成...")
        
        # 創建包含多個項目的測試數據
        items = []
        for i in range(15):  # 15個項目，應該生成3頁
            items.append({
                'item_no': i + 1,
                'model': f'MULTI-PAGE-{i+1:03d}',
                'description': f'Multi-page test item {i+1} with detailed description for testing pagination and layout consistency across multiple pages. This item demonstrates the professional formatting capabilities.',
                'quantity': 1.0 + (i % 3),
                'unit': 'SET',
                'unit_price': 2000.00 + (i * 500),
                'discount': i % 5,
                'amount': (1.0 + (i % 3)) * (2000.00 + (i * 500)) * (1 - (i % 5) / 100)
            })
        
        multi_page_data = {
            'quotation_no': 'WEASY-MULTI-001',
            'date': 'MAY 28, 25',
            'valid_until': 'JUNE 28, 25',
            'customer_name': 'MULTI-PAGE TEST COMPANY LIMITED',
            'contact_person': 'MR. MULTI PAGE TESTER',
            'address': 'Multi-Page Test Address, Professional Building, Test District',
            'phone': '+852 9999 0000',
            'fax': '+852 9999 0001',
            'sales_person': 'MULTI PAGE SALES',
            'contract_no': 'HB-ENG-2025-MULTI',
            'contract_description': 'MULTI-PAGE PDF GENERATION TEST CONTRACT',
            'items': items,
            'total_amount': sum(item['amount'] for item in items),
            'terms': '30% DEPOSIT, 70% UPON COMPLETION',
            'delivery_terms': 'APPROX. 8 WEEKS',
            'pricing_terms': 'C.I.F. HONG KONG',
            'remarks': 'This is a multi-page test document to verify pagination and layout consistency.'
        }
        
        print(f"📊 項目數量: {len(multi_page_data['items'])}")
        
        # 計算預期頁數
        expected_pages = pdf_generator._calculate_pages_needed(multi_page_data['items'])
        print(f"📄 預期頁數: {expected_pages}")
        
        # 生成多頁面PDF
        multi_pdf = pdf_generator.generate_quotation_pdf(multi_page_data)
        
        if multi_pdf and os.path.exists(multi_pdf):
            file_size = os.path.getsize(multi_pdf)
            print(f"✅ 多頁面PDF生成成功: {multi_pdf}")
            print(f"📄 文件大小: {file_size:,} bytes")
            return True
        else:
            print("❌ 多頁面PDF生成失敗")
            return False
            
    except Exception as e:
        print(f"❌ 多頁面測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試WeasyPrint PDF生成器...")
    print("=" * 60)
    
    # 測試WeasyPrint安裝
    weasy_installed = test_weasyprint_installation()
    if not weasy_installed:
        print("\n💡 安裝說明:")
        print("   Windows: pip install weasyprint")
        print("   Linux: sudo apt-get install python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0")
        print("          pip install weasyprint")
        print("   macOS: brew install pango && pip install weasyprint")
        return False
    
    print()
    
    # 測試基本功能
    basic_test = test_weasyprint_generator()
    
    # 測試PDF管理器
    manager_test = test_pdf_manager()
    
    # 測試多頁面功能
    multi_page_test = test_multi_page_generation()
    
    print("\n" + "=" * 60)
    
    if basic_test and manager_test and multi_page_test:
        print("🎉 所有測試通過！")
        print("📝 WeasyPrint PDF生成器已成功配置")
        print("🔍 請檢查output目錄中的PDF文件")
        print("\n💡 WeasyPrint優勢:")
        print("   ✅ 專業的PDF輸出質量")
        print("   ✅ 精確的CSS控制")
        print("   ✅ 完美的字體和佈局支持")
        print("   ✅ 原生PDF格式，無需轉換")
        print("   ✅ 支持複雜的多頁面文檔")
        print("   ✅ 符合商業文檔標準")
        return True
    else:
        print("💥 部分測試失敗！")
        print("請檢查錯誤信息並解決問題")
        return False

if __name__ == "__main__":
    success = main()
    print("\n按任意鍵退出...")
    input()
    sys.exit(0 if success else 1) 