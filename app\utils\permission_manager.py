from PyQt5.QtWidgets import QMessageBox
from app.utils.language_manager import LanguageManager

class PermissionManager:
    """權限管理工具類，用於檢查用戶是否有權限執行特定操作"""
    
    ROLE_ADMIN = 'admin'  # 管理員角色
    ROLE_MANAGER = 'manager'  # 高級用戶角色
    ROLE_USER = 'user'  # 一般用戶角色
    
    def __init__(self, parent=None, user_role=None):
        self.parent = parent  # 父視窗，用於顯示提示信息
        self.user_role = user_role  # 當前用戶角色
        self.lang_manager = LanguageManager()
        
    def can_modify(self):
        """檢查是否有修改權限（管理員和高級用戶）"""
        return self.user_role in [self.ROLE_ADMIN, self.ROLE_MANAGER]
        
    def can_manage_users(self):
        """檢查是否有用戶管理權限（僅管理員）"""
        return self.user_role == self.ROLE_ADMIN
        
    def check_modify_permission(self, show_message=True):
        """檢查修改權限，如果沒有權限且需要顯示信息，則顯示提示對話框"""
        has_permission = self.can_modify()
        if not has_permission and show_message and self.parent:
            QMessageBox.warning(
                self.parent,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('insufficient_permissions')
            )
        return has_permission
        
    def check_user_management_permission(self, show_message=True):
        """檢查用戶管理權限，如果沒有權限且需要顯示信息，則顯示提示對話框"""
        has_permission = self.can_manage_users()
        if not has_permission and show_message and self.parent:
            QMessageBox.warning(
                self.parent,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('admin_only_feature')
            )
        return has_permission
        
    def update_ui_for_role(self, buttons=None, editable_widgets=None):
        """根據用戶角色更新界面元素的啟用狀態"""
        can_modify = self.can_modify()
        
        # 更新按鈕
        if buttons:
            for btn in buttons:
                if btn:
                    # 僅當按鈕是修改操作按鈕時才禁用
                    if hasattr(btn, 'action_type') and btn.action_type == 'modify':
                        btn.setEnabled(can_modify)
                        if not can_modify:
                            # 設置禁用按鈕為灰色
                            btn.setStyleSheet("QPushButton { color: #aaaaaa; background-color: #f0f0f0; }")
                            btn.setToolTip(self.lang_manager.get_text('insufficient_permissions'))
                        else:
                            # 恢復正常樣式
                            btn.setStyleSheet("")
        
        # 更新可編輯控件
        if editable_widgets:
            for widget in editable_widgets:
                if widget:
                    widget.setEnabled(can_modify) 