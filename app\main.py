import sys
import os

# 添加應用程式根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTranslator, QLocale
from app.ui.main_window import MainWindow
from app.database.db_manager import DatabaseManager

# 全局變量，用於存儲主窗口實例
global_main_window = None

def main():
    global global_main_window
    
    app = QApplication(sys.argv)
    
    # 設置應用程式樣式
    app.setStyle('Fusion')
    
    # 初始化數據庫
    db_manager = DatabaseManager()
    db_manager.connect()
    db_manager.init_database()
    db_manager.disconnect()
    
    # 創建並顯示主視窗
    window = MainWindow()
    global_main_window = window  # 保存到全局變量
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 