"""
資源管理器
統一管理應用程式的資源，包括數據庫連接、文件路徑、配置等
"""

import os
import sqlite3
from contextlib import contextmanager
from configparser import ConfigParser
from app.utils.logger import system_logger

class ResourceManager:
    """資源管理器類"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ResourceManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.config = ConfigParser()
        self.config.read('config/settings.ini', encoding='utf-8')
        
        # 初始化路徑
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.app_dir = os.path.join(self.base_dir, 'app')
        self.assets_dir = os.path.join(self.app_dir, 'assets')
        self.icons_dir = os.path.join(self.assets_dir, 'icons')
        self.images_dir = os.path.join(self.assets_dir, 'images')
        self.database_dir = os.path.join(self.base_dir, 'database_file')
        self.output_dir = os.path.join(self.base_dir, 'output')
        self.logs_dir = os.path.join(self.base_dir, 'logs')
        
        # 確保所有必要目錄存在
        self._ensure_directories()
        
        system_logger.info("資源管理器初始化完成")
    
    def _ensure_directories(self):
        """確保所有必要的目錄存在"""
        directories = [
            self.assets_dir,
            self.icons_dir,
            self.images_dir,
            self.database_dir,
            self.output_dir,
            self.logs_dir
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                system_logger.info(f"創建目錄: {directory}")
    
    def get_database_path(self):
        """獲取數據庫文件路徑"""
        return self.config['Database']['path']
    
    def get_icon_path(self, icon_name):
        """獲取圖標文件路徑"""
        return os.path.join(self.icons_dir, f"{icon_name}.png")
    
    def get_image_path(self, image_name):
        """獲取圖片文件路徑"""
        return os.path.join(self.images_dir, image_name)
    
    def get_output_path(self, filename):
        """獲取輸出文件路徑"""
        return os.path.join(self.output_dir, filename)
    
    def get_company_info(self):
        """獲取公司信息"""
        return {
            'name_cn': self.config['CompanyInfo']['name_cn'],
            'name_en': self.config['CompanyInfo']['name_en'],
            'address_cn': self.config['CompanyInfo']['address_cn'],
            'address_en': self.config['CompanyInfo']['address_en'],
            'nature_cn': self.config['CompanyInfo']['nature_cn'],
            'nature_en': self.config['CompanyInfo']['nature_en'],
            'phone': self.config['CompanyInfo']['phone'],
            'fax': self.config['CompanyInfo']['fax'],
            'email': self.config['CompanyInfo']['email'],
            'logo_path': self.config['CompanyInfo']['logo_path']
        }
    
    def get_numbering_config(self):
        """獲取編號配置"""
        return {
            'quotation_prefix': self.config['Numbering']['quotation_prefix'],
            'invoice_prefix': self.config['Numbering']['invoice_prefix'],
            'delivery_note_prefix': self.config['Numbering']['delivery_note_prefix']
        }
    
    def get_default_language(self):
        """獲取默認語言"""
        return self.config['Appearance']['default_language']
    
    @contextmanager
    def get_database_connection(self):
        """獲取數據庫連接的上下文管理器"""
        conn = None
        try:
            db_path = self.get_database_path()
            conn = sqlite3.connect(db_path, timeout=5.0)
            conn.execute("PRAGMA foreign_keys = ON")
            system_logger.debug(f"數據庫連接已建立: {db_path}")
            yield conn
        except Exception as e:
            system_logger.error(f"數據庫連接錯誤: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
                system_logger.debug("數據庫連接已關閉")
    
    def cleanup_old_logs(self, days_to_keep=30):
        """清理舊的日誌文件"""
        import time
        import glob
        
        try:
            log_pattern = os.path.join(self.logs_dir, "*.log")
            log_files = glob.glob(log_pattern)
            
            current_time = time.time()
            cutoff_time = current_time - (days_to_keep * 24 * 60 * 60)
            
            deleted_count = 0
            for log_file in log_files:
                if os.path.getmtime(log_file) < cutoff_time:
                    os.remove(log_file)
                    deleted_count += 1
                    system_logger.info(f"刪除舊日誌文件: {log_file}")
            
            if deleted_count > 0:
                system_logger.info(f"清理完成，刪除了 {deleted_count} 個舊日誌文件")
                
        except Exception as e:
            system_logger.error(f"清理日誌文件時發生錯誤: {str(e)}")
    
    def get_temp_file_path(self, filename):
        """獲取臨時文件路徑"""
        import tempfile
        temp_dir = tempfile.gettempdir()
        return os.path.join(temp_dir, filename)
    
    def validate_file_path(self, file_path):
        """驗證文件路徑是否安全"""
        # 防止路徑遍歷攻擊
        normalized_path = os.path.normpath(file_path)
        if '..' in normalized_path or normalized_path.startswith('/'):
            system_logger.warning(f"檢測到不安全的文件路徑: {file_path}")
            return False
        return True

# 全局資源管理器實例
resource_manager = ResourceManager() 