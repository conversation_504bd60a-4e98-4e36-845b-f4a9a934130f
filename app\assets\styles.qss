/* 全局樣式設定 */
QMainWindow, QWidget {
    background-color: #f5f7fa;
    color: #333333;
    font-family: "Microsoft JhengHei UI", "微軟正黑體", "Arial", sans-serif;
}

/* 導航按鈕樣式 */
QPushButton.nav-button {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px;
    margin: 5px;
    font-weight: bold;
    min-height: 30px;
    text-align: left;
}

QPushButton.nav-button:hover {
    background-color: #2980b9;
}

QPushButton.nav-button:pressed {
    background-color: #1c6ea4;
}

/* 普通按鈕樣式 */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1c6ea4;
}

/* 表格樣式 */
QTableWidget {
    background-color: white;
    alternate-background-color: #f9f9f9;
    border: 1px solid #dddddd;
    border-radius: 4px;
}

QTableWidget::item {
    padding: 5px;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #e9eef2;
    padding: 6px;
    border: 1px solid #dddddd;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-weight: bold;
}

/* 工具欄樣式 */
QToolBar {
    background-color: #34495e;
    border: none;
    spacing: 3px;
    padding: 5px;
}

/* 標籤樣式 */
QLabel {
    color: #2c3e50;
}

QLabel#welcome-label {
    font-size: 16pt;
    color: #34495e;
    padding: 20px;
}

QLabel.section-header {
    font-size: 14pt;
    font-weight: bold;
    color: #34495e;
    padding-bottom: 10px;
}

/* 對話框樣式 */
QDialog {
    background-color: #f5f7fa;
}

/* 輸入框樣式 */
QLineEdit, QTextEdit, QComboBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
    border: 1px solid #3498db;
}

/* 選項卡樣式 */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    top: -1px;
}

QTabBar::tab {
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QTabBar::tab:hover:!selected {
    background-color: #d6eaf8;
} 