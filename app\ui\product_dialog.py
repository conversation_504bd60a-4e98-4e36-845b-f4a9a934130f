from PyQt5.QtWidgets import QDialog, QFormLayout, QLineEdit, QPushButton, QHBoxLayout, QVBoxLayout, QDoubleSpinBox
from app.utils.language_manager import LanguageManager

class ProductDialog(QDialog):
    def __init__(self, parent=None, product_data=None):
        super().__init__(parent)
        self.product_data = product_data  # 如果為None則是新增，否則是編輯
        self.lang_manager = LanguageManager()
        self.init_ui()
        
        # 如果是編輯模式，填充表單
        if self.product_data:
            self.fill_form()
            
    def init_ui(self):
        title = self.lang_manager.get_text('edit_product') if self.product_data else self.lang_manager.get_text('add_product')
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        
        # 表單佈局
        form_layout = QFormLayout()
        
        # 產品編號
        self.code_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('product_code'), self.code_edit)
        
        # 產品名稱
        self.name_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('product_name'), self.name_edit)
        
        # 規格
        self.specification_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('specification'), self.specification_edit)
        
        # 單位
        self.unit_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('unit'), self.unit_edit)
        
        # 價格
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setRange(0, 999999.99)
        self.price_edit.setDecimals(2)
        self.price_edit.setSingleStep(0.1)
        form_layout.addRow(self.lang_manager.get_text('price'), self.price_edit)
        
        layout.addLayout(form_layout)
        
        # 按鈕佈局
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton(self.lang_manager.get_text('save'))
        self.cancel_btn = QPushButton(self.lang_manager.get_text('cancel'))
        
        btn_layout.addStretch()
        btn_layout.addWidget(self.save_btn)
        btn_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(btn_layout)
        
        # 連接信號
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
    def fill_form(self):
        """用現有產品數據填充表單"""
        self.code_edit.setText(self.product_data[1])
        self.name_edit.setText(self.product_data[2])
        self.specification_edit.setText(self.product_data[3] or '')
        self.unit_edit.setText(self.product_data[4] or '')
        self.price_edit.setValue(float(self.product_data[5]))
        
    def get_data(self):
        """獲取用戶輸入的數據"""
        return {
            'code': self.code_edit.text(),
            'name': self.name_edit.text(),
            'specification': self.specification_edit.text(),
            'unit': self.unit_edit.text(),
            'price': self.price_edit.value()
        } 