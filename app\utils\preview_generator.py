from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTextBrowser, QLabel, QFrame, QScrollArea, QWidget)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QPixmap, QImage
from PyQt5.QtPrintSupport import QPrinter, QPrintPreviewDialog
import configparser
import os
from datetime import datetime

class PreviewGenerator:
    """預覽生成器類，用於生成報價單、發票和送貨單的預覽"""
    
    def __init__(self):
        # 讀取公司信息
        self.config = configparser.ConfigParser()
        self.config.read('config/settings.ini', encoding='utf-8')
    
    def generate_quotation_preview(self, quotation_data):
        """生成報價單預覽界面"""
        preview_dialog = QuotationPreviewDialog(quotation_data)
        return preview_dialog.exec_()

class QuotationPreviewDialog(QDialog):
    """報價單預覽對話框"""
    
    def __init__(self, quotation_data):
        super().__init__(None, Qt.Window)
        self.quotation_data = quotation_data
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"預覽報價單 - {self.quotation_data.get('quotation_no', '')}")
        self.resize(800, 900)  # A4比例接近
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 創建滾動區域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 創建內容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 添加報價單內容
        self._add_company_header(content_layout)
        self._add_quotation_header(content_layout)
        self._add_customer_info(content_layout)
        self._add_title(content_layout)
        self._add_items_table(content_layout)
        self._add_total(content_layout)
        self._add_payment_terms(content_layout)
        self._add_footer(content_layout)
        
        # 設置滾動區域的內容
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # 按鈕布局
        buttons_layout = QHBoxLayout()
        
        self.btn_print = QPushButton("打印")
        self.btn_print.clicked.connect(self.print_preview)
        
        self.btn_close = QPushButton("關閉")
        self.btn_close.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_print)
        buttons_layout.addWidget(self.btn_close)
        
        main_layout.addLayout(buttons_layout)
    
    def _add_company_header(self, layout):
        """添加公司Logo和信息"""
        header_layout = QHBoxLayout()
        
        # 公司信息布局
        company_info_layout = QVBoxLayout()
        
        # 公司名稱
        company_name = QLabel("H. B. ENGINEERING COMPANY")
        company_name.setFont(QFont("Arial", 14, QFont.Bold))
        company_name.setStyleSheet("color: blue;")
        company_info_layout.addWidget(company_name)
        
        # 中文名稱
        config = configparser.ConfigParser()
        config.read('config/settings.ini', encoding='utf-8')
        company_name_cn = config.get('CompanyInfo', 'name_cn', fallback='蒼藍工程公司')
        
        company_name_cn_label = QLabel(company_name_cn)
        company_name_cn_label.setFont(QFont("Arial", 10))
        company_info_layout.addWidget(company_name_cn_label)
        
        # 地址
        address_en = config.get('CompanyInfo', 'address_en', fallback='No. 40, GF Yeung Uk San Chuen,\nTung Chau Yuen Long, N.T.')
        address_label = QLabel(f"地址: {address_en}")
        address_label.setFont(QFont("Arial", 10))
        company_info_layout.addWidget(address_label)
        
        # 電話和傳真
        phone = config.get('CompanyInfo', 'phone', fallback='60173180')
        fax = config.get('CompanyInfo', 'fax', fallback='60173180')
        
        contact_label = QLabel(f"電話: {phone}  傳真: {fax}")
        contact_label.setFont(QFont("Arial", 10))
        company_info_layout.addWidget(contact_label)
        
        # 添加到頭部布局
        header_layout.addLayout(company_info_layout)
        header_layout.addStretch()
        
        # 添加Logo（如果有）
        logo_path = config.get('CompanyInfo', 'logo_path', fallback='app/assets/images/logo.png')
        if os.path.exists(logo_path):
            logo_label = QLabel()
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            header_layout.addWidget(logo_label)
        
        layout.addLayout(header_layout)
        
    def _add_quotation_header(self, layout):
        """添加報價單編號和日期"""
        header_layout = QHBoxLayout()
        header_layout.addStretch()
        
        # 報價單編號和日期
        info_layout = QVBoxLayout()
        
        # 報價單編號
        quotation_no_label = QLabel(f"報價單號碼 Q.N No: {self.quotation_data.get('quotation_no', 'None')}")
        quotation_no_label.setFont(QFont("Arial", 10))
        info_layout.addWidget(quotation_no_label)
        
        # 日期
        date_label = QLabel(f"日期 DATE: {self.quotation_data.get('date', datetime.now().strftime('%Y-%m-%d'))}")
        date_label.setFont(QFont("Arial", 10))
        info_layout.addWidget(date_label)
        
        header_layout.addLayout(info_layout)
        layout.addLayout(header_layout)
        
    def _add_customer_info(self, layout):
        """添加客戶信息"""
        customer_layout = QVBoxLayout()
        
        # 添加"致 TO:"標籤
        to_layout = QHBoxLayout()
        to_label = QLabel("致 TO:")
        to_label.setFont(QFont("Arial", 10, QFont.Bold))
        to_layout.addWidget(to_label)
        
        # 添加客戶名稱
        customer_name = QLabel(self.quotation_data.get('customer_name', ''))
        customer_name.setFont(QFont("Arial", 10))
        to_layout.addWidget(customer_name)
        to_layout.addStretch()
        
        customer_layout.addLayout(to_layout)
        
        # 添加客戶地址
        address_layout = QHBoxLayout()
        address_label = QLabel("地址 ADDRESS:")
        address_label.setFont(QFont("Arial", 10))
        address_layout.addWidget(address_label)
        
        address_value = QLabel(self.quotation_data.get('address', ''))
        address_value.setFont(QFont("Arial", 10))
        address_layout.addWidget(address_value)
        address_layout.addStretch()
        
        customer_layout.addLayout(address_layout)
        
        # 添加聯繫人
        contact_layout = QHBoxLayout()
        contact_label = QLabel("聯絡人 ATTN:")
        contact_label.setFont(QFont("Arial", 10))
        contact_layout.addWidget(contact_label)
        
        contact_value = QLabel(self.quotation_data.get('contact_person', ''))
        contact_value.setFont(QFont("Arial", 10))
        contact_layout.addWidget(contact_value)
        contact_layout.addStretch()
        
        customer_layout.addLayout(contact_layout)
        
        # 添加電話
        phone_layout = QHBoxLayout()
        phone_label = QLabel("電話 TEL:")
        phone_label.setFont(QFont("Arial", 10))
        phone_layout.addWidget(phone_label)
        
        phone_value = QLabel(self.quotation_data.get('phone', ''))
        phone_value.setFont(QFont("Arial", 10))
        phone_layout.addWidget(phone_value)
        phone_layout.addStretch()
        
        customer_layout.addLayout(phone_layout)
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        layout.addLayout(customer_layout)
        layout.addWidget(separator)
        
    def _add_title(self, layout):
        """添加標題"""
        title_layout = QHBoxLayout()
        title_layout.addStretch()
        
        title_label = QLabel("QUOTATION")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: blue;")
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
    def _add_items_table(self, layout):
        """添加項目表格"""
        from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        
        items_table = QTableWidget()
        items_table.setColumnCount(5)
        items_table.setHorizontalHeaderLabels(["項目", "描述", "數量", "單價", "金額 (HKD)"])
        
        # 設置表格樣式
        items_table.horizontalHeader().setFont(QFont("Arial", 10, QFont.Bold))
        items_table.horizontalHeader().setStretchLastSection(True)
        items_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        items_table.verticalHeader().setVisible(False)
        
        # 設置列寬
        items_table.setColumnWidth(0, 40)   # 項目
        items_table.setColumnWidth(1, 250)  # 描述
        items_table.setColumnWidth(2, 80)   # 數量
        items_table.setColumnWidth(3, 100)  # 單價
        items_table.setColumnWidth(4, 120)  # 金額
        
        # 填充表格數據
        items = self.quotation_data.get('items', [])
        items_table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            # 項目編號
            item_no = QTableWidgetItem(str(item.get('item_no', '')))
            items_table.setItem(row, 0, item_no)
            
            # 描述
            description = QTableWidgetItem(str(item.get('description', '')))
            items_table.setItem(row, 1, description)
            
            # 數量
            quantity = QTableWidgetItem(str(item.get('quantity', '')))
            quantity.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            items_table.setItem(row, 2, quantity)
            
            # 單價
            unit_price = QTableWidgetItem(f"${item.get('unit_price', 0):.2f}")
            unit_price.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            items_table.setItem(row, 3, unit_price)
            
            # 金額
            amount = item.get('quantity', 0) * item.get('unit_price', 0) * (1 - item.get('discount', 0) / 100)
            amount_item = QTableWidgetItem(f"${amount:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            items_table.setItem(row, 4, amount_item)
        
        layout.addWidget(items_table)
        
    def _add_total(self, layout):
        """添加總計"""
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        
        # 總計標籤
        total_label = QLabel("總計 TOTAL:")
        total_label.setFont(QFont("Arial", 10, QFont.Bold))
        total_layout.addWidget(total_label)
        
        # 總金額
        total_amount = self.quotation_data.get('total_amount', 0)
        total_value = QLabel(f"${total_amount:.2f}")
        total_value.setFont(QFont("Arial", 10, QFont.Bold))
        total_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_value.setMinimumWidth(120)
        
        total_layout.addWidget(total_value)
        
        layout.addLayout(total_layout)
        
    def _add_payment_terms(self, layout):
        """添加付款條款"""
        terms_layout = QVBoxLayout()
        
        # 付款條款標籤
        terms_label = QLabel("付款條件 Payment Terms:")
        terms_label.setFont(QFont("Arial", 10, QFont.Bold))
        terms_layout.addWidget(terms_label)
        
        # 付款條款內容
        terms_text = self.quotation_data.get('terms', '')
        terms_value = QLabel(terms_text if terms_text else "")
        terms_value.setFont(QFont("Arial", 10))
        terms_value.setWordWrap(True)
        
        terms_layout.addWidget(terms_value)
        
        layout.addLayout(terms_layout)
        
    def _add_footer(self, layout):
        """添加頁尾（簽名區和有效期）"""
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
        
        # 簽名區
        signature_layout = QHBoxLayout()
        
        # 公司簽名區
        company_signature = QVBoxLayout()
        
        company_label = QLabel("H.B. ENGINEERING COMPANY")
        company_label.setFont(QFont("Arial", 10))
        company_signature.addWidget(company_label)
        
        signature_line1 = QFrame()
        signature_line1.setFrameShape(QFrame.HLine)
        signature_line1.setFrameShadow(QFrame.Sunken)
        company_signature.addWidget(signature_line1)
        
        auth_signature = QLabel("授權簽署 Authorized Signature")
        auth_signature.setFont(QFont("Arial", 10))
        company_signature.addWidget(auth_signature)
        
        name_label1 = QLabel("姓名 Name:")
        name_label1.setFont(QFont("Arial", 10))
        company_signature.addWidget(name_label1)
        
        date_label1 = QLabel("日期 Date:")
        date_label1.setFont(QFont("Arial", 10))
        company_signature.addWidget(date_label1)
        
        # 客戶簽名區
        customer_signature = QVBoxLayout()
        
        customer_label = QLabel(self.quotation_data.get('customer_name', ''))
        customer_label.setFont(QFont("Arial", 10))
        customer_signature.addWidget(customer_label)
        
        signature_line2 = QFrame()
        signature_line2.setFrameShape(QFrame.HLine)
        signature_line2.setFrameShadow(QFrame.Sunken)
        customer_signature.addWidget(signature_line2)
        
        customer_sign = QLabel("客戶簽署及公司印章")
        customer_sign.setFont(QFont("Arial", 10))
        customer_signature.addWidget(customer_sign)
        
        name_label2 = QLabel("姓名 Name:")
        name_label2.setFont(QFont("Arial", 10))
        customer_signature.addWidget(name_label2)
        
        date_label2 = QLabel("日期 Date:")
        date_label2.setFont(QFont("Arial", 10))
        customer_signature.addWidget(date_label2)
        
        # 添加到簽名布局
        signature_layout.addLayout(company_signature)
        signature_layout.addStretch()
        signature_layout.addLayout(customer_signature)
        
        layout.addLayout(signature_layout)
        
        # 添加有效期說明和頁碼
        footer_layout = QHBoxLayout()
        
        valid_days = 30  # 默認30天有效期
        valid_label = QLabel(f"此報價有效期為{valid_days}天。This quotation is valid for {valid_days} days.")
        valid_label.setFont(QFont("Arial", 8))
        footer_layout.addWidget(valid_label)
        
        footer_layout.addStretch()
        
        page_label = QLabel("頁數 Page 1/1")
        page_label.setFont(QFont("Arial", 8))
        footer_layout.addWidget(page_label)
        
        layout.addLayout(footer_layout)
        
    def print_preview(self):
        """打印預覽"""
        printer = QPrinter(QPrinter.HighResolution)
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(self.print_document)
        preview.exec_()
        
    def print_document(self, printer):
        """打印文檔"""
        # 創建一個QTextDocument並設置其HTML內容
        from PyQt5.QtWidgets import QTextBrowser
        document = QTextBrowser()
        
        # 構建HTML內容
        html_content = self._generate_html_content()
        document.setHtml(html_content)
        
        # 打印文檔
        document.print_(printer)
        
    def _generate_html_content(self):
        """生成用於打印的HTML內容"""
        # 這裡可以生成與預覽相同格式的HTML，但為了簡化，我們使用一個基本的HTML模板
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .header {{ display: flex; justify-content: space-between; }}
                .company-name {{ color: blue; font-size: 16pt; font-weight: bold; }}
                .title {{ color: blue; font-size: 18pt; font-weight: bold; text-align: center; margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th {{ background-color: #f2f2f2; text-align: left; padding: 8px; }}
                td {{ padding: 8px; }}
                .amount {{ text-align: right; }}
                .total {{ font-weight: bold; text-align: right; }}
                .signature {{ display: flex; justify-content: space-between; margin-top: 50px; }}
                .signature-box {{ width: 45%; }}
                .signature-line {{ border-top: 1px solid black; margin: 30px 0 5px 0; }}
                .footer {{ display: flex; justify-content: space-between; font-size: 8pt; margin-top: 30px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div>
                    <div class="company-name">H. B. ENGINEERING COMPANY</div>
                    <div>蒼藍工程公司</div>
                    <div>地址: No. 40, GF Yeung Uk San Chuen, Tung Chau Yuen Long, N.T.</div>
                    <div>電話: 60173180  傳真: 60173180</div>
                </div>
            </div>
            
            <div style="text-align: right;">
                <div>報價單號碼 Q.N No: {self.quotation_data.get('quotation_no', 'None')}</div>
                <div>日期 DATE: {self.quotation_data.get('date', '2024-12-20')}</div>
            </div>
            
            <div>
                <div><strong>致 TO:</strong> {self.quotation_data.get('customer_name', '')}</div>
                <div><strong>地址 ADDRESS:</strong> {self.quotation_data.get('address', '')}</div>
                <div><strong>聯絡人 ATTN:</strong> {self.quotation_data.get('contact_person', '')}</div>
                <div><strong>電話 TEL:</strong> {self.quotation_data.get('phone', '')}</div>
            </div>
            
            <hr>
            
            <div class="title">QUOTATION</div>
            
            <table border="1">
                <tr>
                    <th>項目</th>
                    <th>描述</th>
                    <th>數量</th>
                    <th>單價</th>
                    <th>金額 (HKD)</th>
                </tr>
        """
        
        # 添加項目行
        for item in self.quotation_data.get('items', []):
            amount = item.get('quantity', 0) * item.get('unit_price', 0) * (1 - item.get('discount', 0) / 100)
            html += f"""
                <tr>
                    <td>{item.get('item_no', '')}</td>
                    <td>{item.get('description', '')}</td>
                    <td class="amount">{item.get('quantity', '')}</td>
                    <td class="amount">${item.get('unit_price', 0):.2f}</td>
                    <td class="amount">${amount:.2f}</td>
                </tr>
            """
        
        # 添加總計
        total_amount = self.quotation_data.get('total_amount', 0)
        html += f"""
            </table>
            
            <div class="total">總計 TOTAL: ${total_amount:.2f}</div>
            
            <div style="margin-top: 20px;">
                <strong>付款條件 Payment Terms:</strong>
                <div>{self.quotation_data.get('terms', '')}</div>
            </div>
            
            <div class="signature">
                <div class="signature-box">
                    <div>H.B. ENGINEERING COMPANY</div>
                    <div class="signature-line"></div>
                    <div>授權簽署 Authorized Signature</div>
                    <div>姓名 Name:</div>
                    <div>日期 Date:</div>
                </div>
                
                <div class="signature-box">
                    <div>{self.quotation_data.get('customer_name', '')}</div>
                    <div class="signature-line"></div>
                    <div>客戶簽署及公司印章</div>
                    <div>姓名 Name:</div>
                    <div>日期 Date:</div>
                </div>
            </div>
            
            <div class="footer">
                <div>此報價有效期為30天。This quotation is valid for 30 days.</div>
                <div>頁數 Page 1/1</div>
            </div>
        </body>
        </html>
        """
        
        return html 