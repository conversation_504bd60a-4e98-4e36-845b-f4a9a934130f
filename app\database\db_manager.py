import sqlite3
import os
import time
from configparser import ConfigParser
from app.utils.language_manager import LanguageManager
from app.utils.logger import system_logger

class DatabaseManager:
    """數據庫管理器，提供數據庫連接和操作功能"""
    
    def __init__(self, max_retries=3, retry_delay=0.5, connection_timeout=5.0):
        """
        初始化數據庫管理器
        
        參數:
            max_retries: 操作失敗時最大重試次數
            retry_delay: 重試間隔時間（秒）
            connection_timeout: 連接超時時間（秒）
        """
        self.config = ConfigParser()
        self.config.read('config/settings.ini', encoding='utf-8')
        self.db_path = self.config['Database']['path']
        self.conn = None
        self.cursor = None
        self.lang_manager = LanguageManager()
        
        # 連接池和錯誤處理參數
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.connection_timeout = connection_timeout
        
        # 記錄初始化信息
        system_logger.info(f"數據庫管理器初始化，數據庫路徑: {self.db_path}")
        
    def connect(self):
        """連接到數據庫，支持重試機制"""
        retries = 0
        while retries < self.max_retries:
            try:
                # 如果已經連接，則先斷開
                if self.conn:
                    try:
                        self.disconnect()
                    except Exception as e:
                        system_logger.warning(f"斷開現有連接時發生錯誤: {str(e)}")
                        # 繼續執行，不影響新連接的建立
                        
                # 確保數據庫目錄存在
                db_dir = os.path.dirname(self.db_path)
                if not os.path.exists(db_dir):
                    system_logger.info(f"創建數據庫目錄: {db_dir}")
                    os.makedirs(db_dir, exist_ok=True)
                
                # 嘗試建立新連接
                system_logger.debug(f"連接到數據庫: {self.db_path}")
                self.conn = sqlite3.connect(self.db_path, timeout=self.connection_timeout)
                
                # 啟用外鍵約束
                self.conn.execute("PRAGMA foreign_keys = ON")
                
                # 設置繁忙超時
                self.conn.execute(f"PRAGMA busy_timeout = {int(self.connection_timeout * 1000)}")
                
                # 確保連接成功後創建游標
                if self.conn:
                    self.cursor = self.conn.cursor()
                    system_logger.info("數據庫連接成功")
                    return True
                else:
                    system_logger.error("數據庫連接失敗")
                    self.conn = None
                    self.cursor = None
                    return False
                    
            except sqlite3.Error as e:
                retries += 1
                system_logger.warning(f"數據庫連接嘗試 {retries}/{self.max_retries} 失敗: {str(e)}")
                
                # 檢查是否為連接錯誤
                if "database is locked" in str(e) or "no such table" in str(e) or "not connected" in str(e):
                    # 如果是連接問題，嘗試重新連接
                    system_logger.info("嘗試重新連接數據庫")
                    self.disconnect()
                    if not self.connect():
                        system_logger.error("重新連接數據庫失敗")
                        break
                
                if retries < self.max_retries:
                    time.sleep(self.retry_delay)
                else:
                    system_logger.error(f"數據庫連接失敗，已達最大重試次數: {str(e)}")
                    self.conn = None
                    self.cursor = None
                    return False
                    
        system_logger.error("數據庫連接失敗，超過重試次數")
        return False
            
    def disconnect(self):
        """關閉數據庫連接"""
        try:
            if self.conn:
                self.conn.close()
                self.conn = None
                self.cursor = None
                system_logger.debug("數據庫連接已關閉")
        except Exception as e:
            system_logger.error(f"斷開數據庫連接時發生錯誤: {str(e)}")
            
    def execute_with_retry(self, func, *args, **kwargs):
        """
        執行數據庫操作，支持自動重試
        
        參數:
            func: 要執行的函數
            *args, **kwargs: 傳遞給函數的參數
            
        返回:
            函數執行結果
        """
        retries = 0
        while retries < self.max_retries:
            try:
                return func(*args, **kwargs)
            except sqlite3.Error as e:
                retries += 1
                system_logger.warning(f"數據庫操作嘗試 {retries}/{self.max_retries} 失敗: {str(e)}")
                
                # 檢查是否為連接錯誤
                if "database is locked" in str(e) or "no such table" in str(e) or "not connected" in str(e):
                    # 如果是連接問題，嘗試重新連接
                    system_logger.info("嘗試重新連接數據庫")
                    self.disconnect()
                    if not self.connect():
                        system_logger.error("重新連接數據庫失敗")
                        break
                
                if retries < self.max_retries:
                    time.sleep(self.retry_delay)
                else:
                    system_logger.error(f"數據庫操作失敗，已達最大重試次數: {str(e)}")
                    raise
        
        raise sqlite3.Error(f"數據庫操作失敗，已達最大重試次數 {self.max_retries}")
            
    def init_database(self):
        """初始化數據庫，創建必要的表格"""
        try:
            # 嘗試連接到數據庫
            connection_successful = self.connect()
            if not connection_successful:
                system_logger.error("初始化數據庫失敗：無法連接到數據庫")
                return False
                
            system_logger.info("開始初始化數據庫")
            
            # 創建客戶資料表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    contact_person TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 創建產品資料表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT,
                    name TEXT NOT NULL,
                    specification TEXT,
                    unit TEXT DEFAULT '個',
                    price REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 創建使用者資料表，包含角色權限控制
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    role TEXT NOT NULL DEFAULT 'user',
                    status TEXT NOT NULL DEFAULT 'active',
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 創建回收站表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS recycle_bin (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_type TEXT NOT NULL,
                    item_id INTEGER NOT NULL,
                    item_data TEXT NOT NULL,
                    item_title TEXT NOT NULL,
                    deleted_by TEXT,
                    deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    original_table TEXT NOT NULL
                )
            """)
            
            # 檢查是否已存在管理員帳戶，若不存在則創建默認管理員
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            count_result = self.cursor.fetchone()
            
            if count_result and count_result[0] == 0:
                system_logger.info("創建默認管理員帳戶")
                import hashlib
                # 使用 SHA-256 儲存密碼雜湊值
                password_hash = hashlib.sha256('admin'.encode()).hexdigest()
                self.cursor.execute("""
                    INSERT INTO users (username, password_hash, full_name, role, status)
                    VALUES (?, ?, ?, ?, ?)
                """, ('admin', password_hash, '系統管理員', 'admin', 'active'))
                self.conn.commit()
                system_logger.info("默認管理員帳戶創建成功")
            
            # 提交所有更改
            self.conn.commit()
            system_logger.info("數據庫初始化完成")
            
            # 關閉連接
            self.disconnect()
            
            return True
        except Exception as e:
            system_logger.error(f"初始化數據庫錯誤: {str(e)}", exc_info=True)
            
            # 確保連接關閉
            if self.conn:
                try:
                    # 回滾任何未完成的事務
                    self.conn.rollback()
                except sqlite3.Error as e:
                    system_logger.warning(f"回滾事務時發生錯誤: {str(e)}")
                self.disconnect()
                
            return False
            
    def get_all_customers(self):
        """獲取所有客戶資料"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM customers ORDER BY id DESC')
            customers = self.cursor.fetchall()
            self.disconnect()
            return customers
        except sqlite3.Error as e:
            system_logger.error(f"數據庫查詢錯誤 - 獲取客戶資料: {str(e)}")
            return []
        except Exception as e:
            system_logger.error(f"獲取客戶資料時發生未預期錯誤: {str(e)}")
            return []
            
    def get_customers(self):
        """獲取所有客戶資料（以字典列表形式返回）"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM customers ORDER BY id DESC')
            rows = self.cursor.fetchall()
            customers = []
            
            # 將結果轉換為字典列表格式
            for row in rows:
                customers.append({
                    'id': row[0],
                    'name': row[1],
                    'contact_person': row[2],
                    'phone': row[3],
                    'email': row[4],
                    'address': row[5]
                })
                
            self.disconnect()
            return customers
        except sqlite3.Error as e:
            system_logger.error(f"數據庫查詢錯誤 - 獲取客戶列表: {str(e)}")
            return []
        except Exception as e:
            system_logger.error(f"獲取客戶列表時發生未預期錯誤: {str(e)}")
            return []
            
    def add_customer(self, customer_data):
        """添加新客戶"""
        try:
            self.connect()
            self.cursor.execute('''
                INSERT INTO customers (name, contact_person, phone, email, address)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                customer_data['name'],
                customer_data['contact_person'],
                customer_data['phone'],
                customer_data['email'],
                customer_data['address']
            ))
            self.conn.commit()
            last_id = self.cursor.lastrowid
            self.disconnect()
            return last_id
        except sqlite3.IntegrityError as e:
            system_logger.error(f"數據完整性錯誤 - 添加客戶: {str(e)}")
            return None
        except sqlite3.Error as e:
            system_logger.error(f"數據庫錯誤 - 添加客戶: {str(e)}")
            return None
        except Exception as e:
            system_logger.error(f"添加客戶時發生未預期錯誤: {str(e)}")
            return None
            
    def update_customer(self, customer_id, customer_data):
        """更新客戶資料"""
        try:
            self.connect()
            self.cursor.execute('''
                UPDATE customers 
                SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?
                WHERE id = ?
            ''', (
                customer_data['name'],
                customer_data['contact_person'],
                customer_data['phone'],
                customer_data['email'],
                customer_data['address'],
                customer_id
            ))
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            self.disconnect()
            return rows_affected > 0
        except Exception as e:
            system_logger.error(f"更新客戶錯誤: {str(e)}")
            return False
            
    def delete_customer(self, customer_id):
        """刪除客戶"""
        try:
            self.connect()
            self.cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            self.disconnect()
            return rows_affected > 0
        except Exception as e:
            system_logger.error(f"刪除客戶錯誤: {str(e)}")
            return False
            
    def get_customer(self, customer_id):
        """獲取指定ID的客戶資料"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
            row = self.cursor.fetchone()
            self.disconnect()
            
            if row:
                # 將結果轉換為字典格式
                customer = {
                    'id': row[0],
                    'name': row[1],
                    'contact_person': row[2],
                    'phone': row[3],
                    'email': row[4],
                    'address': row[5]
                }
                return customer
            return None
        except Exception as e:
            system_logger.error(f"獲取客戶資料錯誤: {str(e)}")
            return None

    # 產品管理相關方法
    def get_all_products(self):
        """獲取所有產品資料"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM products ORDER BY id DESC')
            products = self.cursor.fetchall()
            self.disconnect()
            return products
        except Exception as e:
            system_logger.error(f"獲取產品資料錯誤: {str(e)}")
            return []
            
    def add_product(self, product_data):
        """添加新產品"""
        try:
            self.connect()
            self.cursor.execute('''
                INSERT INTO products (code, name, specification, unit, price)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                product_data['code'],
                product_data['name'],
                product_data['specification'],
                product_data['unit'],
                product_data['price']
            ))
            self.conn.commit()
            last_id = self.cursor.lastrowid
            self.disconnect()
            return last_id
        except Exception as e:
            system_logger.error(f"添加產品錯誤: {str(e)}")
            return None
            
    def update_product(self, product_id, product_data):
        """更新產品資料"""
        try:
            self.connect()
            self.cursor.execute('''
                UPDATE products 
                SET code = ?, name = ?, specification = ?, unit = ?, price = ?
                WHERE id = ?
            ''', (
                product_data['code'],
                product_data['name'],
                product_data['specification'],
                product_data['unit'],
                product_data['price'],
                product_id
            ))
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            self.disconnect()
            return rows_affected > 0
        except Exception as e:
            system_logger.error(f"更新產品錯誤: {str(e)}")
            return False
            
    def delete_product(self, product_id):
        """刪除產品"""
        try:
            self.connect()
            self.cursor.execute('DELETE FROM products WHERE id = ?', (product_id,))
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            self.disconnect()
            return rows_affected > 0
        except Exception as e:
            system_logger.error(f"刪除產品錯誤: {str(e)}")
            return False
            
    def get_product(self, product_id):
        """獲取指定ID的產品資料"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
            row = self.cursor.fetchone()
            self.disconnect()
            
            if row:
                # 將結果轉換為字典格式
                product = {
                    'id': row[0],
                    'code': row[1],
                    'name': row[2],
                    'specification': row[3],
                    'unit': row[4],
                    'price': row[5]
                }
                return product
            return None
        except Exception as e:
            system_logger.error(f"獲取產品資料錯誤: {str(e)}")
            return None
            
    def get_products(self):
        """獲取所有產品資料（以字典列表形式返回）"""
        try:
            self.connect()
            self.cursor.execute('SELECT * FROM products ORDER BY id DESC')
            rows = self.cursor.fetchall()
            products = []
            
            # 將結果轉換為字典列表格式
            for row in rows:
                products.append({
                    'id': row[0],
                    'code': row[1],
                    'name': row[2],
                    'specification': row[3],
                    'unit': row[4],
                    'price': row[5]
                })
                
            self.disconnect()
            return products
        except Exception as e:
            system_logger.error(f"獲取產品資料錯誤: {str(e)}")
            return []
            
    # 報價單相關方法
    def get_quotations(self, search_text="", date_from=None, date_to=None, status=None):
        """獲取報價單列表"""
        try:
            self.connect()
            query = """
                SELECT q.id, q.quotation_no, c.name as customer_name, q.date, q.valid_until, 
                       q.total_amount, q.status
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.id
                WHERE 1=1
            """
            params = []
            
            # 添加搜索條件
            if search_text:
                query += " AND (q.quotation_no LIKE ? OR c.name LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
            
            # 添加日期範圍
            if date_from:
                query += " AND q.date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND q.date <= ?"
                params.append(date_to)
            
            # 添加狀態過濾
            if status:
                query += " AND q.status = ?"
                params.append(status)
            
            query += " ORDER BY q.date DESC, q.id DESC"
            
            self.cursor.execute(query, params)
            result = self.cursor.fetchall()
            
            # 轉換為字典列表
            quotations = []
            for row in result:
                quotations.append({
                    'id': row[0],
                    'quotation_no': row[1],
                    'customer_name': row[2],
                    'date': row[3],
                    'valid_until': row[4],
                    'total_amount': row[5],
                    'status': row[6]
                })
            
            self.disconnect()
            return quotations
        except Exception as e:
            system_logger.error(f"獲取報價單列表錯誤: {str(e)}")
            return []

    def get_quotation(self, quotation_id):
        """獲取指定ID的報價單詳情"""
        try:
            self.connect()
            
            # 獲取基本信息
            self.cursor.execute("""
                SELECT q.*, c.name as customer_name
                FROM quotations q
                LEFT JOIN customers c ON q.customer_id = c.id
                WHERE q.id = ?
            """, (quotation_id,))
            
            quotation = self.cursor.fetchone()
            
            if not quotation:
                self.disconnect()
                return None
            
            # 根據數據庫設計，獲取正確的列索引
            # 這裡假設quotation表結構為：
            # id, quotation_no, customer_id, date, valid_until, total_amount, status, created_by, created_at
            result = {
                'id': quotation[0],
                'quotation_no': quotation[1],
                'customer_id': quotation[2],
                'date': quotation[3],
                'valid_until': quotation[4],
                'total_amount': quotation[5],
                'status': quotation[6],
                'customer_name': quotation[9]  # 這是從JOIN獲取的列
            }
            
            # 獲取客戶詳情
            if result['customer_id']:
                self.cursor.execute("""
                    SELECT * FROM customers WHERE id = ?
                """, (result['customer_id'],))
                
                customer = self.cursor.fetchone()
                if customer:
                    result['contact_person'] = customer[2]
                    result['phone'] = customer[3]
                    result['address'] = customer[5]
            
            # 獲取報價單項目
            self.cursor.execute("""
                SELECT * FROM quotation_items WHERE quotation_id = ? ORDER BY id
            """, (quotation_id,))
            
            items = self.cursor.fetchall()
            result['items'] = []
            
            for i, item in enumerate(items):
                # 假設項目表結構為：
                # id, quotation_id, product_id, description, quantity, unit_price, discount, amount
                item_data = {
                    'item_no': i + 1,
                    'product_id': item[2],
                    'description': item[3],
                    'quantity': item[4],
                    'unit': 'SET',  # 這裡可能需要從產品表獲取
                    'unit_price': item[5],
                    'discount': item[6]
                }
                
                # 獲取產品詳情（如果有產品ID）
                if item_data['product_id']:
                    self.cursor.execute("""
                        SELECT * FROM products WHERE id = ?
                    """, (item_data['product_id'],))
                    
                    product = self.cursor.fetchone()
                    if product:
                        item_data['product_name'] = product[2]  # 假設產品名稱在第3列
                        item_data['unit'] = product[4]  # 假設單位在第5列
                
                result['items'].append(item_data)
            
            self.disconnect()
            return result
        except Exception as e:
            system_logger.error(f"獲取報價單詳情錯誤: {str(e)}")
            return None

    def add_quotation(self, quotation_data):
        """添加新報價單"""
        try:
            self.connect()
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 插入報價單主表
            self.cursor.execute("""
                INSERT INTO quotations (
                    quotation_no, customer_id, date, valid_until, 
                    total_amount, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                quotation_data['quotation_no'],
                quotation_data['customer_id'],
                quotation_data['date'],
                quotation_data['valid_until'],
                quotation_data['total_amount'],
                quotation_data['status']
            ))
            
            quotation_id = self.cursor.lastrowid
            
            # 插入報價單項目
            for item in quotation_data['items']:
                amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                
                self.cursor.execute("""
                    INSERT INTO quotation_items (
                        quotation_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    quotation_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    amount
                ))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            return quotation_id
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"添加報價單錯誤: {str(e)}")
            return None

    def generate_new_quotation_number(self):
        """生成新的報價單編號"""
        try:
            from datetime import datetime
            
            # 獲取配置中的報價單前綴
            self.config.read('config/settings.ini', encoding='utf-8')
            prefix = self.config['Numbering']['quotation_prefix']
            
            # 當前年月
            current_yearmonth = datetime.now().strftime('%Y%m')
            
            self.connect()
            
            # 查詢當前年月的最大編號
            self.cursor.execute("""
                SELECT MAX(quotation_no) FROM quotations 
                WHERE quotation_no LIKE ?
            """, (f"{prefix}-{current_yearmonth}-%",))
            
            result = self.cursor.fetchone()[0]
            
            if result:
                # 提取序號部分並加1
                seq_no = int(result.split('-')[-1]) + 1
            else:
                # 從1開始
                seq_no = 1
            
            # 生成新編號，格式：QT-YYYYMM-XXXX
            new_quotation_no = f"{prefix}-{current_yearmonth}-{seq_no:04d}"
            
            self.disconnect()
            return new_quotation_no
        except Exception as e:
            system_logger.error(f"生成新的報價單編號錯誤: {str(e)}")
            return None

    def update_quotation(self, quotation_id, quotation_data):
        """更新報價單"""
        try:
            self.connect()
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 更新報價單主表
            self.cursor.execute("""
                UPDATE quotations SET
                    quotation_no = ?,
                    customer_id = ?,
                    date = ?,
                    valid_until = ?,
                    total_amount = ?,
                    status = ?
                WHERE id = ?
            """, (
                quotation_data['quotation_no'],
                quotation_data['customer_id'],
                quotation_data['date'],
                quotation_data['valid_until'],
                quotation_data['total_amount'],
                quotation_data['status'],
                quotation_id
            ))
            
            # 刪除原有的報價單項目
            self.cursor.execute("DELETE FROM quotation_items WHERE quotation_id = ?", (quotation_id,))
            
            # 插入新的報價單項目
            for item in quotation_data['items']:
                amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                
                self.cursor.execute("""
                    INSERT INTO quotation_items (
                        quotation_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    quotation_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    amount
                ))
            
            # 提交事務
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            self.disconnect()
            return rows_affected > 0
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"更新報價單錯誤: {str(e)}")
            return False

    def delete_quotation(self, quotation_id, deleted_by="system"):
        """刪除報價單（移動到回收站）"""
        try:
            # 先獲取報價單完整數據
            quotation_data = self.get_quotation(quotation_id)
            if not quotation_data:
                return False
            
            # 移動到回收站
            recycle_id = self.move_to_recycle_bin(
                item_type="quotation",
                item_id=quotation_id,
                item_data=quotation_data,
                item_title=f"報價單 {quotation_data['quotation_no']}",
                deleted_by=deleted_by,
                original_table="quotations"
            )
            
            if recycle_id:
                # 從原表刪除
                self.connect()
                self.conn.execute("BEGIN TRANSACTION")
                
                # 刪除報價單項目
                self.cursor.execute("DELETE FROM quotation_items WHERE quotation_id = ?", (quotation_id,))
                
                # 刪除報價單主表
                self.cursor.execute("DELETE FROM quotations WHERE id = ?", (quotation_id,))
                
                self.conn.commit()
                self.disconnect()
                return True
            
            return False
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"刪除報價單錯誤: {str(e)}")
            return False

    def convert_quotation_to_invoice(self, quotation_id):
        """將報價單轉換為發票"""
        try:
            # 獲取報價單詳情
            quotation_data = self.get_quotation(quotation_id)
            if not quotation_data:
                return None
            
            # 生成新的發票編號
            from datetime import datetime
            
            # 獲取配置中的發票前綴
            self.config.read('config/settings.ini', encoding='utf-8')
            prefix = self.config['Numbering']['invoice_prefix']
            
            # 當前年月
            current_yearmonth = datetime.now().strftime('%Y%m')
            
            self.connect()
            
            # 檢查invoices表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    payment_due DATE,
                    total_amount REAL NOT NULL,
                    payment_status TEXT NOT NULL,
                    related_quotation_id INTEGER,
                    notes TEXT,
                    terms TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (related_quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 檢查invoice_items表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER,
                    description TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    discount REAL DEFAULT 0,
                    amount REAL NOT NULL,
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # 查詢當前年月的最大編號
            self.cursor.execute("""
                SELECT MAX(invoice_no) FROM invoices 
                WHERE invoice_no LIKE ?
            """, (f"{prefix}-{current_yearmonth}-%",))
            
            result = self.cursor.fetchone()[0]
            
            if result:
                # 提取序號部分並加1
                seq_no = int(result.split('-')[-1]) + 1
            else:
                # 從1開始
                seq_no = 1
            
            # 生成新編號，格式：INV-YYYYMM-XXXX
            invoice_no = f"{prefix}-{current_yearmonth}-{seq_no:04d}"
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 插入發票主表
            self.cursor.execute("""
                INSERT INTO invoices (
                    invoice_no, customer_id, date, payment_due, 
                    total_amount, payment_status, related_quotation_id, notes, terms, created_at
                ) VALUES (?, ?, ?, ?, ?, 'unpaid', ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                invoice_no,
                quotation_data['customer_id'],
                datetime.now().strftime('%Y-%m-%d'),  # 今天的日期
                datetime.now().strftime('%Y-%m-%d'),  # 暫用今天作為到期日
                quotation_data['total_amount'],
                quotation_id,
                '',  # notes
                ''   # terms
            ))
            
            invoice_id = self.cursor.lastrowid
            
            # 複製報價單項目到發票項目
            for item in quotation_data['items']:
                amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                
                self.cursor.execute("""
                    INSERT INTO invoice_items (
                        invoice_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    amount
                ))
            
            # 更新報價單狀態為"已轉換"
            self.cursor.execute("""
                UPDATE quotations SET status = ? WHERE id = ?
            """, ('已轉換', quotation_id))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            
            return {
                'id': invoice_id,
                'invoice_no': invoice_no
            }
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"報價單轉換為發票錯誤: {str(e)}")
            return None

    def convert_quotation_to_delivery(self, quotation_id):
        """將報價單轉換為送貨單"""
        try:
            # 獲取報價單詳情
            quotation_data = self.get_quotation(quotation_id)
            if not quotation_data:
                return None
            
            # 生成新的送貨單編號
            delivery_no = self.generate_new_delivery_note_number()
            
            self.connect()
            
            # 檢查delivery_notes表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS delivery_notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    status TEXT NOT NULL,
                    invoice_id INTEGER,
                    quotation_id INTEGER,
                    delivery_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 檢查delivery_items表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS delivery_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_id INTEGER NOT NULL,
                    product_id INTEGER,
                    description TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    FOREIGN KEY (delivery_id) REFERENCES delivery_notes (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            from datetime import datetime
            
            # 插入送貨單主表
            self.cursor.execute("""
                INSERT INTO delivery_notes (
                    delivery_no, customer_id, date, 
                    status, quotation_id, delivery_address, created_at
                ) VALUES (?, ?, ?, 'preparing', ?, ?, CURRENT_TIMESTAMP)
            """, (
                delivery_no,
                quotation_data['customer_id'],
                datetime.now().strftime('%Y-%m-%d'),  # 今天的日期
                quotation_id,
                quotation_data['address']  # 使用客戶地址作為送貨地址
            ))
            
            delivery_id = self.cursor.lastrowid
            
            # 複製報價單項目到送貨單項目
            for item in quotation_data['items']:
                self.cursor.execute("""
                    INSERT INTO delivery_items (
                        delivery_id, product_id, description, 
                        quantity
                    ) VALUES (?, ?, ?, ?)
                """, (
                    delivery_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity']
                ))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            
            return {
                'id': delivery_id,
                'delivery_no': delivery_no
            }
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"報價單轉換為送貨單錯誤: {str(e)}")
            return None

    def get_invoices(self, search_text="", date_from=None, date_to=None, status=None):
        """獲取發票列表，支持搜索和過濾"""
        try:
            self.connect()
            
            # 檢查invoices表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    payment_due DATE,
                    total_amount REAL NOT NULL,
                    payment_status TEXT NOT NULL,
                    related_quotation_id INTEGER,
                    notes TEXT,
                    terms TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (related_quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 基本查詢
            query = """
                SELECT i.id, i.invoice_no, c.name as customer_name, i.date, 
                       i.payment_due, i.total_amount, i.payment_status
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE 1=1
            """
            params = []
            
            # 搜索條件
            if search_text:
                query += " AND (i.invoice_no LIKE ? OR c.name LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
            
            # 日期過濾
            if date_from:
                query += " AND i.date >= ?"
                params.append(date_from)
                
            if date_to:
                query += " AND i.date <= ?"
                params.append(date_to)
            
            # 狀態過濾
            if status:
                query += " AND i.payment_status = ?"
                params.append(status)
                
            # 按日期降序排序
            query += " ORDER BY i.date DESC"
            
            # 執行查詢
            self.cursor.execute(query, tuple(params))
            invoices = []
            
            for row in self.cursor.fetchall():
                invoices.append({
                    'id': row[0],
                    'invoice_no': row[1],
                    'customer_name': row[2] or '',
                    'date': row[3],
                    'payment_due': row[4],
                    'total_amount': row[5],
                    'payment_status': row[6]
                })
                
            self.disconnect()
            return invoices
        except Exception as e:
            system_logger.error(f"獲取發票列表錯誤: {str(e)}")
            return []
    
    def get_invoice(self, invoice_id):
        """獲取發票詳情"""
        try:
            self.connect()
            
            # 獲取發票主表信息
            self.cursor.execute("""
                SELECT i.id, i.invoice_no, i.customer_id, c.name as customer_name, 
                       c.contact_person, c.phone, c.address,
                       i.date, i.payment_due, i.total_amount, i.payment_status,
                       i.related_quotation_id, i.notes, i.terms
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.id = ?
            """, (invoice_id,))
            
            invoice_row = self.cursor.fetchone()
            if not invoice_row:
                self.disconnect()
                return None
                
            # 獲取發票項目
            self.cursor.execute("""
                SELECT ii.id, ii.product_id, p.name as product_name, ii.description,
                       ii.quantity, ii.unit_price, ii.discount, ii.amount
                FROM invoice_items ii
                LEFT JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            """, (invoice_id,))
            
            items = []
            for item_row in self.cursor.fetchall():
                items.append({
                    'id': item_row[0],
                    'product_id': item_row[1],
                    'product_name': item_row[2] or '',
                    'description': item_row[3],
                    'quantity': float(item_row[4]),
                    'unit_price': float(item_row[5]),
                    'discount': float(item_row[6]),
                    'amount': float(item_row[7])
                })
            
            # 計算小計和折扣
            subtotal = sum(item['amount'] for item in items)
            
            # 構建發票數據
            invoice_data = {
                'id': invoice_row[0],
                'invoice_no': invoice_row[1],
                'customer_id': invoice_row[2],
                'customer_name': invoice_row[3] or '',
                'contact_person': invoice_row[4] or '',
                'phone': invoice_row[5] or '',
                'address': invoice_row[6] or '',
                'date': invoice_row[7],
                'payment_due': invoice_row[8],
                'total_amount': float(invoice_row[9]),
                'payment_status': invoice_row[10],
                'related_quotation_id': invoice_row[11],
                'notes': invoice_row[12] or '',
                'terms': invoice_row[13] or '',
                'subtotal': subtotal,
                'discount_percentage': 0 if subtotal == 0 else 100 * (1 - float(invoice_row[9]) / subtotal),
                'discount_amount': subtotal - float(invoice_row[9]),
                'items': items
            }
            
            self.disconnect()
            return invoice_data
        except Exception as e:
            system_logger.error(f"獲取發票詳情錯誤: {str(e)}")
            return None
            
    def add_invoice(self, invoice_data):
        """添加新發票"""
        try:
            self.connect()
            
            # 檢查invoices表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    payment_due DATE,
                    total_amount REAL NOT NULL,
                    payment_status TEXT NOT NULL,
                    related_quotation_id INTEGER,
                    notes TEXT,
                    terms TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (related_quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 檢查invoice_items表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER,
                    description TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    discount REAL DEFAULT 0,
                    amount REAL NOT NULL,
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 插入發票主表
            self.cursor.execute("""
                INSERT INTO invoices (
                    invoice_no, customer_id, date, payment_due, total_amount, 
                    payment_status, related_quotation_id, notes, terms
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                invoice_data['invoice_no'],
                invoice_data.get('customer_id'),
                invoice_data['date'],
                invoice_data['payment_due'],
                invoice_data['total_amount'],
                invoice_data['payment_status'],
                invoice_data.get('related_quotation_id'),
                invoice_data.get('notes', ''),
                invoice_data.get('terms', '')
            ))
            
            invoice_id = self.cursor.lastrowid
            
            # 插入發票項目
            for item in invoice_data.get('items', []):
                # 計算實際金額
                amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                
                self.cursor.execute("""
                    INSERT INTO invoice_items (
                        invoice_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    amount
                ))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            
            return True
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"添加發票錯誤: {str(e)}")
            return False
            
    def update_invoice(self, invoice_id, invoice_data):
        """更新發票資料"""
        try:
            self.connect()
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 更新發票主表
            self.cursor.execute("""
                UPDATE invoices SET 
                    invoice_no = ?, 
                    customer_id = ?, 
                    date = ?, 
                    payment_due = ?, 
                    total_amount = ?, 
                    payment_status = ?, 
                    related_quotation_id = ?, 
                    notes = ?, 
                    terms = ?
                WHERE id = ?
            """, (
                invoice_data['invoice_no'],
                invoice_data.get('customer_id'),
                invoice_data['date'],
                invoice_data['payment_due'],
                invoice_data['total_amount'],
                invoice_data['payment_status'],
                invoice_data.get('related_quotation_id'),
                invoice_data.get('notes', ''),
                invoice_data.get('terms', ''),
                invoice_id
            ))
            
            # 刪除原有項目
            self.cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
            
            # 插入新項目
            for item in invoice_data.get('items', []):
                # 計算實際金額
                amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
                
                self.cursor.execute("""
                    INSERT INTO invoice_items (
                        invoice_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    amount
                ))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            
            return True
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"更新發票錯誤: {str(e)}")
            return False
            
    def delete_invoice(self, invoice_id, deleted_by="system"):
        """刪除發票（移動到回收站）"""
        try:
            # 先獲取發票完整數據
            invoice_data = self.get_invoice(invoice_id)
            if not invoice_data:
                return False
            
            # 移動到回收站
            recycle_id = self.move_to_recycle_bin(
                item_type="invoice",
                item_id=invoice_id,
                item_data=invoice_data,
                item_title=f"發票 {invoice_data['invoice_no']}",
                deleted_by=deleted_by,
                original_table="invoices"
            )
            
            if recycle_id:
                # 從原表刪除
                self.connect()
                self.conn.execute("BEGIN TRANSACTION")
                
                # 刪除關聯的項目
                self.cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
                
                # 刪除發票本身
                self.cursor.execute("DELETE FROM invoices WHERE id = ?", (invoice_id,))
                
                self.conn.commit()
                self.disconnect()
                return True
            
            return False
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"刪除發票錯誤: {str(e)}")
            return False
            
    def convert_invoice_to_delivery(self, invoice_id):
        """將發票轉換為送貨單"""
        try:
            # 獲取發票詳情
            invoice_data = self.get_invoice(invoice_id)
            if not invoice_data:
                return None
            
            # 生成新的送貨單編號
            delivery_no = self.generate_new_delivery_note_number()
            
            self.connect()
            
            # 檢查delivery_notes表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS delivery_notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    status TEXT NOT NULL,
                    invoice_id INTEGER,
                    quotation_id INTEGER,
                    delivery_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 檢查delivery_items表是否存在，如果不存在則創建
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS delivery_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_id INTEGER NOT NULL,
                    product_id INTEGER,
                    description TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    FOREIGN KEY (delivery_id) REFERENCES delivery_notes (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            from datetime import datetime
            
            # 插入送貨單主表
            self.cursor.execute("""
                INSERT INTO delivery_notes (
                    delivery_no, customer_id, date, 
                    status, invoice_id, delivery_address, created_at
                ) VALUES (?, ?, ?, 'preparing', ?, ?, CURRENT_TIMESTAMP)
            """, (
                delivery_no,
                invoice_data['customer_id'],
                datetime.now().strftime('%Y-%m-%d'),  # 今天的日期
                invoice_id,
                invoice_data['address']  # 使用客戶地址作為送貨地址
            ))
            
            delivery_id = self.cursor.lastrowid
            
            # 複製發票項目到送貨單項目
            for item in invoice_data['items']:
                self.cursor.execute("""
                    INSERT INTO delivery_items (
                        delivery_id, product_id, description, 
                        quantity
                    ) VALUES (?, ?, ?, ?)
                """, (
                    delivery_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity']
                ))
            
            # 提交事務
            self.conn.commit()
            self.disconnect()
            
            return {
                'id': delivery_id,
                'delivery_no': delivery_no
            }
        except Exception as e:
            # 回滾事務
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"發票轉換為送貨單錯誤: {str(e)}")
            return None
            
    def generate_new_invoice_number(self):
        """生成新的發票編號"""
        try:
            from datetime import datetime
            
            # 獲取配置中的發票前綴
            self.config.read('config/settings.ini', encoding='utf-8')
            prefix = self.config['Numbering']['invoice_prefix']
            
            # 當前年月
            current_yearmonth = datetime.now().strftime('%Y%m')
            
            self.connect()
            
            # 檢查發票表是否存在
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    payment_due DATE,
                    total_amount REAL NOT NULL,
                    payment_status TEXT NOT NULL,
                    related_quotation_id INTEGER,
                    notes TEXT,
                    terms TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (related_quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 查詢當前年月的最大編號
            self.cursor.execute("""
                SELECT MAX(invoice_no) FROM invoices 
                WHERE invoice_no LIKE ?
            """, (f"{prefix}-{current_yearmonth}-%",))
            
            result = self.cursor.fetchone()[0]
            
            if result:
                # 提取序號部分並加1
                seq_no = int(result.split('-')[-1]) + 1
            else:
                # 從1開始
                seq_no = 1
            
            # 生成新編號，格式：INV-YYYYMM-XXXX
            invoice_no = f"{prefix}-{current_yearmonth}-{seq_no:04d}"
            
            self.disconnect()
            return invoice_no
        except Exception as e:
            system_logger.error(f"生成發票編號錯誤: {str(e)}")
            return f"{prefix}-{current_yearmonth}-0001"  # 出錯時返回默認編號
            
    def generate_new_delivery_note_number(self):
        """生成新的送貨單編號"""
        try:
            from datetime import datetime
            
            # 獲取配置中的送貨單前綴
            self.config.read('config/settings.ini', encoding='utf-8')
            prefix = self.config['Numbering']['delivery_note_prefix']
            
            # 當前年月
            current_yearmonth = datetime.now().strftime('%Y%m')
            
            self.connect()
            
            # 檢查送貨單表是否存在
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS delivery_notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_no TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    date DATE NOT NULL,
                    status TEXT NOT NULL,
                    invoice_id INTEGER,
                    quotation_id INTEGER,
                    delivery_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (quotation_id) REFERENCES quotations (id)
                )
            """)
            
            # 查詢當前年月的最大編號
            self.cursor.execute("""
                SELECT MAX(delivery_no) FROM delivery_notes 
                WHERE delivery_no LIKE ?
            """, (f"{prefix}-{current_yearmonth}-%",))
            
            result = self.cursor.fetchone()[0]
            
            if result:
                # 提取序號部分並加1
                seq_no = int(result.split('-')[-1]) + 1
            else:
                # 從1開始
                seq_no = 1
            
            # 生成新編號，格式：DN-YYYYMM-XXXX
            delivery_no = f"{prefix}-{current_yearmonth}-{seq_no:04d}"
            
            self.disconnect()
            return delivery_no
        except Exception as e:
            system_logger.error(f"生成送貨單編號錯誤: {str(e)}")
            return f"{prefix}-{current_yearmonth}-0001"  # 出錯時返回默認編號
            
    def get_delivery_notes(self, search_text="", date_from=None, date_to=None, status=None):
        """獲取送貨單列表，可以根據條件篩選"""
        try:
            self.connect()
            
            # 基礎SQL語句
            sql = """
                SELECT d.id, d.delivery_no, c.name, d.date, d.status, d.delivery_address, c.id
                FROM delivery_notes d
                LEFT JOIN customers c ON d.customer_id = c.id
                WHERE 1=1
            """
            params = []
            
            # 添加搜索條件
            if search_text:
                sql += " AND (d.delivery_no LIKE ? OR c.name LIKE ?)"
                params.extend([f'%{search_text}%', f'%{search_text}%'])
                
            # 添加日期範圍
            if date_from:
                sql += " AND d.date >= ?"
                params.append(date_from)
            if date_to:
                sql += " AND d.date <= ?"
                params.append(date_to)
                
            # 添加狀態篩選
            if status:
                sql += " AND d.status = ?"
                params.append(status)
                
            # 按日期降序排序
            sql += " ORDER BY d.date DESC"
            
            # 執行查詢
            self.cursor.execute(sql, params)
            rows = self.cursor.fetchall()
            
            # 格式化結果為字典列表
            delivery_notes = []
            for row in rows:
                delivery_notes.append({
                    'id': row[0],
                    'delivery_note_no': row[1],
                    'customer_name': row[2],
                    'delivery_date': row[3],
                    'delivery_status': row[4],
                    'delivery_address': row[5],
                    'customer_id': row[6]
                })
                
            self.disconnect()
            return delivery_notes
        except Exception as e:
            system_logger.error(f"獲取送貨單列表錯誤: {str(e)}")
            return []
            
    def get_delivery_note(self, delivery_id):
        """獲取單個送貨單詳情"""
        try:
            self.connect()
            
            # 獲取送貨單主表資料
            self.cursor.execute("""
                SELECT d.id, d.delivery_no, d.customer_id, c.name, d.date, 
                       d.status, d.delivery_address, d.invoice_id, d.quotation_id,
                       c.contact_person, c.phone, c.address
                FROM delivery_notes d
                LEFT JOIN customers c ON d.customer_id = c.id
                WHERE d.id = ?
            """, (delivery_id,))
            
            row = self.cursor.fetchone()
            
            if not row:
                self.disconnect()
                return None
                
            # 將結果格式化為字典
            delivery_data = {
                'id': row[0],
                'delivery_no': row[1],
                'customer_id': row[2],
                'customer_name': row[3],
                'date': row[4],
                'delivery_status': row[5],
                'delivery_address': row[6] or row[11],  # 如果送貨地址為空，則使用客戶地址
                'invoice_id': row[7],
                'quotation_id': row[8],
                'contact_person': row[9],
                'phone': row[10],
                'items': []
            }
            
            # 獲取送貨單項目
            self.cursor.execute("""
                SELECT di.id, di.product_id, di.description, di.quantity,
                       p.name, p.code, p.unit
                FROM delivery_items di
                LEFT JOIN products p ON di.product_id = p.id
                WHERE di.delivery_id = ?
            """, (delivery_id,))
            
            items = self.cursor.fetchall()
            
            # 添加項目到送貨單數據
            for item in items:
                delivery_data['items'].append({
                    'id': item[0],
                    'product_id': item[1],
                    'description': item[2],
                    'quantity': item[3],
                    'product_name': item[4],
                    'product_code': item[5],
                    'unit': item[6]
                })
                
            self.disconnect()
            return delivery_data
        except Exception as e:
            system_logger.error(f"獲取送貨單詳情錯誤: {str(e)}")
            return None

    def delete_delivery_note(self, delivery_id, deleted_by="system"):
        """刪除送貨單（移動到回收站）"""
        try:
            # 先獲取送貨單完整數據
            delivery_data = self.get_delivery_note(delivery_id)
            if not delivery_data:
                return False
            
            # 移動到回收站
            recycle_id = self.move_to_recycle_bin(
                item_type="delivery_note",
                item_id=delivery_id,
                item_data=delivery_data,
                item_title=f"送貨單 {delivery_data['delivery_no']}",
                deleted_by=deleted_by,
                original_table="delivery_notes"
            )
            
            if recycle_id:
                # 從原表刪除
                self.connect()
                self.conn.execute("BEGIN TRANSACTION")
                
                # 刪除送貨單項目
                self.cursor.execute("DELETE FROM delivery_items WHERE delivery_id = ?", (delivery_id,))
                
                # 刪除送貨單主表
                self.cursor.execute("DELETE FROM delivery_notes WHERE id = ?", (delivery_id,))
                
                self.conn.commit()
                self.disconnect()
                return True
            
            return False
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"刪除送貨單錯誤: {str(e)}")
            return False

    def check_invoice_converted_to_delivery(self, invoice_id):
        """檢查發票是否已轉換為送貨單"""
        try:
            self.connect()
            
            # 檢查是否已存在關聯此發票的送貨單
            self.cursor.execute("""
                SELECT COUNT(*) FROM delivery_notes 
                WHERE invoice_id = ?
            """, (invoice_id,))
            
            count = self.cursor.fetchone()[0]
            self.disconnect()
            
            return count > 0
        except Exception as e:
            system_logger.error(f"檢查發票轉換狀態錯誤: {str(e)}")
            return False

    # 用戶管理相關方法
    def authenticate_user(self, username, password):
        """驗證用戶登錄"""
        try:
            self.connect()
            
            # 使用 SHA-256 生成密碼雜湊
            import hashlib
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            self.cursor.execute("""
                SELECT id, username, full_name, role, status
                FROM users
                WHERE username = ? AND password_hash = ? AND status = 'active'
            """, (username, password_hash))
            
            user = self.cursor.fetchone()
            
            if user:
                # 更新最後登錄時間
                import datetime
                self.cursor.execute("""
                    UPDATE users
                    SET last_login = ?
                    WHERE id = ?
                """, (datetime.datetime.now().isoformat(), user[0]))
                self.conn.commit()
                
                # 返回用戶資訊
                user_data = {
                    'id': user[0],
                    'username': user[1],
                    'full_name': user[2],
                    'role': user[3],
                    'status': user[4]
                }
                self.disconnect()
                return user_data
            else:
                self.disconnect()
                return None
        except Exception as e:
            system_logger.error(f"用戶驗證錯誤: {str(e)}")
            return None
    
    def get_users(self):
        """獲取所有用戶清單"""
        try:
            self.connect()
            self.cursor.execute("""
                SELECT id, username, full_name, role, status, last_login, created_at
                FROM users
                ORDER BY username
            """)
            
            users = []
            for row in self.cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'full_name': row[2],
                    'role': row[3],
                    'status': row[4],
                    'last_login': row[5],
                    'created_at': row[6]
                })
            
            self.disconnect()
            return users
        except Exception as e:
            system_logger.error(f"獲取用戶清單錯誤: {str(e)}")
            return []
    
    def get_user(self, user_id):
        """獲取指定用戶詳細資訊"""
        try:
            self.connect()
            self.cursor.execute("""
                SELECT id, username, full_name, role, status, last_login, created_at
                FROM users
                WHERE id = ?
            """, (user_id,))
            
            row = self.cursor.fetchone()
            if not row:
                self.disconnect()
                return None
                
            user = {
                'id': row[0],
                'username': row[1],
                'full_name': row[2],
                'role': row[3],
                'status': row[4],
                'last_login': row[5],
                'created_at': row[6]
            }
            
            self.disconnect()
            return user
        except Exception as e:
            system_logger.error(f"獲取用戶詳細資訊錯誤: {str(e)}")
            return None
    
    def add_user(self, user_data):
        """添加新用戶"""
        try:
            self.connect()
            
            # 檢查用戶名是否已存在
            self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (user_data['username'],))
            if self.cursor.fetchone()[0] > 0:
                self.disconnect()
                return False, "用戶名已存在"
                
            # 生成密碼雜湊
            import hashlib
            password_hash = hashlib.sha256(user_data['password'].encode()).hexdigest()
            
            # 插入新用戶
            self.cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, role, status)
                VALUES (?, ?, ?, ?, ?)
            """, (
                user_data['username'],
                password_hash,
                user_data.get('full_name', ''),
                user_data.get('role', 'user'),
                user_data.get('status', 'active')
            ))
            
            self.conn.commit()
            self.disconnect()
            return True, "用戶創建成功"
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"添加用戶錯誤: {str(e)}")
            return False, f"創建用戶時發生錯誤: {str(e)}"
    
    def update_user(self, user_id, user_data):
        """更新用戶資料"""
        try:
            self.connect()
            
            # 檢查用戶名是否已存在（排除當前用戶）
            if 'username' in user_data:
                self.cursor.execute("""
                    SELECT COUNT(*) FROM users 
                    WHERE username = ? AND id != ?
                """, (user_data['username'], user_id))
                if self.cursor.fetchone()[0] > 0:
                    self.disconnect()
                    return False, "用戶名已存在"
            
            update_fields = []
            params = []
            
            # 更新基本資訊
            if 'username' in user_data:
                update_fields.append("username = ?")
                params.append(user_data['username'])
                
            if 'full_name' in user_data:
                update_fields.append("full_name = ?")
                params.append(user_data['full_name'])
                
            if 'role' in user_data:
                update_fields.append("role = ?")
                params.append(user_data['role'])
                
            if 'status' in user_data:
                update_fields.append("status = ?")
                params.append(user_data['status'])
                
            # 如果有密碼更新
            if 'password' in user_data and user_data['password']:
                import hashlib
                password_hash = hashlib.sha256(user_data['password'].encode()).hexdigest()
                update_fields.append("password_hash = ?")
                params.append(password_hash)
                
            # 如果沒有任何更新字段，直接返回
            if not update_fields:
                self.disconnect()
                return True, "沒有資料需要更新"
                
            # 執行更新
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            params.append(user_id)
            
            self.cursor.execute(query, params)
            self.conn.commit()
            self.disconnect()
            
            return True, "用戶資料更新成功"
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"更新用戶資料錯誤: {str(e)}")
            return False, f"更新用戶資料時發生錯誤: {str(e)}"
    
    def delete_user(self, user_id):
        """刪除用戶"""
        try:
            self.connect()
            
            # 檢查是否為管理員用戶，管理員用戶不能刪除
            self.cursor.execute("SELECT username, role FROM users WHERE id = ?", (user_id,))
            user = self.cursor.fetchone()
            if not user:
                self.disconnect()
                return False, "用戶不存在"
                
            if user[0] == 'admin' or user[1] == 'admin':
                self.disconnect()
                return False, "不能刪除系統管理員"
                
            # 執行刪除
            self.cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
            self.conn.commit()
            self.disconnect()
            
            return True, "用戶已成功刪除"
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"刪除用戶錯誤: {str(e)}")
            return False, f"刪除用戶時發生錯誤: {str(e)}"

    # 回收站管理相關方法
    def move_to_recycle_bin(self, item_type, item_id, item_data, item_title, deleted_by, original_table):
        """將項目移動到回收站"""
        try:
            self.connect()
            
            # 將項目數據序列化為JSON
            import json
            item_data_json = json.dumps(item_data, ensure_ascii=False)
            
            # 插入到回收站
            self.cursor.execute("""
                INSERT INTO recycle_bin (item_type, item_id, item_data, item_title, deleted_by, original_table)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (item_type, item_id, item_data_json, item_title, deleted_by, original_table))
            
            self.conn.commit()
            recycle_id = self.cursor.lastrowid
            self.disconnect()
            
            system_logger.info(f"項目已移動到回收站: {item_type} - {item_title}")
            return recycle_id
        except Exception as e:
            system_logger.error(f"移動到回收站錯誤: {str(e)}")
            return None
    
    def get_recycle_bin_items(self, item_type=None):
        """獲取回收站項目列表"""
        try:
            self.connect()
            
            if item_type:
                self.cursor.execute("""
                    SELECT id, item_type, item_id, item_title, deleted_by, deleted_at, original_table
                    FROM recycle_bin 
                    WHERE item_type = ?
                    ORDER BY deleted_at DESC
                """, (item_type,))
            else:
                self.cursor.execute("""
                    SELECT id, item_type, item_id, item_title, deleted_by, deleted_at, original_table
                    FROM recycle_bin 
                    ORDER BY deleted_at DESC
                """)
            
            items = []
            for row in self.cursor.fetchall():
                items.append({
                    'id': row[0],
                    'item_type': row[1],
                    'item_id': row[2],
                    'item_title': row[3],
                    'deleted_by': row[4],
                    'deleted_at': row[5],
                    'original_table': row[6]
                })
            
            self.disconnect()
            return items
        except Exception as e:
            system_logger.error(f"獲取回收站項目錯誤: {str(e)}")
            return []
    
    def restore_from_recycle_bin(self, recycle_id):
        """從回收站還原項目"""
        try:
            self.connect()
            
            # 開始事務
            self.conn.execute("BEGIN TRANSACTION")
            
            # 獲取回收站項目
            self.cursor.execute("""
                SELECT item_type, item_id, item_data, original_table
                FROM recycle_bin 
                WHERE id = ?
            """, (recycle_id,))
            
            row = self.cursor.fetchone()
            if not row:
                self.conn.rollback()
                self.disconnect()
                return False, "回收站項目不存在"
            
            item_type, item_id, item_data_json, original_table = row
            
            # 反序列化項目數據
            import json
            item_data = json.loads(item_data_json)
            
            # 根據項目類型還原數據
            success = False
            if item_type == "quotation":
                success = self._restore_quotation(item_data)
            elif item_type == "invoice":
                success = self._restore_invoice(item_data)
            elif item_type == "delivery_note":
                success = self._restore_delivery_note(item_data)
            elif item_type == "customer":
                success = self._restore_customer(item_data)
            elif item_type == "product":
                success = self._restore_product(item_data)
            
            if success:
                # 從回收站刪除
                self.cursor.execute("DELETE FROM recycle_bin WHERE id = ?", (recycle_id,))
                self.conn.commit()
                self.disconnect()
                system_logger.info(f"項目已從回收站還原: {item_type} - {item_data.get('title', 'Unknown')}")
                return True, "還原成功"
            else:
                self.conn.rollback()
                self.disconnect()
                return False, "還原失敗"
                
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            system_logger.error(f"從回收站還原錯誤: {str(e)}")
            return False, f"還原錯誤: {str(e)}"
    
    def permanently_delete_from_recycle_bin(self, recycle_id):
        """從回收站永久刪除項目"""
        try:
            self.connect()
            
            # 獲取項目信息用於日誌
            self.cursor.execute("""
                SELECT item_type, item_title
                FROM recycle_bin 
                WHERE id = ?
            """, (recycle_id,))
            
            row = self.cursor.fetchone()
            if row:
                item_type, item_title = row
                
                # 從回收站刪除
                self.cursor.execute("DELETE FROM recycle_bin WHERE id = ?", (recycle_id,))
                self.conn.commit()
                
                system_logger.info(f"項目已永久刪除: {item_type} - {item_title}")
                
            self.disconnect()
            return True
        except Exception as e:
            system_logger.error(f"永久刪除錯誤: {str(e)}")
            return False
    
    def clear_recycle_bin(self, older_than_days=None):
        """清空回收站或清理過期項目"""
        try:
            self.connect()
            
            if older_than_days:
                # 只刪除指定天數之前的項目
                from datetime import datetime, timedelta
                cutoff_date = datetime.now() - timedelta(days=older_than_days)
                
                self.cursor.execute("""
                    DELETE FROM recycle_bin 
                    WHERE deleted_at < ?
                """, (cutoff_date.isoformat(),))
                
                deleted_count = self.cursor.rowcount
                system_logger.info(f"清理了 {deleted_count} 個過期回收站項目（{older_than_days}天前）")
            else:
                # 清空所有項目
                self.cursor.execute("DELETE FROM recycle_bin")
                deleted_count = self.cursor.rowcount
                system_logger.info(f"清空回收站，刪除了 {deleted_count} 個項目")
            
            self.conn.commit()
            self.disconnect()
            return True, deleted_count
        except Exception as e:
            system_logger.error(f"清理回收站錯誤: {str(e)}")
            return False, 0
    
    def _restore_quotation(self, item_data):
        """還原報價單"""
        try:
            # 重新插入報價單主表
            self.cursor.execute("""
                INSERT INTO quotations (
                    quotation_no, customer_id, date, valid_until, 
                    total_amount, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                item_data['quotation_no'],
                item_data['customer_id'],
                item_data['date'],
                item_data['valid_until'],
                item_data['total_amount'],
                item_data['status'],
                item_data.get('created_at', 'CURRENT_TIMESTAMP')
            ))
            
            quotation_id = self.cursor.lastrowid
            
            # 還原報價單項目
            for item in item_data.get('items', []):
                self.cursor.execute("""
                    INSERT INTO quotation_items (
                        quotation_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    quotation_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    item['amount']
                ))
            
            return True
        except Exception as e:
            system_logger.error(f"還原報價單錯誤: {str(e)}")
            return False
    
    def _restore_invoice(self, item_data):
        """還原發票"""
        try:
            # 重新插入發票主表
            self.cursor.execute("""
                INSERT INTO invoices (
                    invoice_no, customer_id, date, payment_due, 
                    total_amount, payment_status, related_quotation_id, 
                    notes, terms, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item_data['invoice_no'],
                item_data['customer_id'],
                item_data['date'],
                item_data['payment_due'],
                item_data['total_amount'],
                item_data['payment_status'],
                item_data.get('related_quotation_id'),
                item_data.get('notes', ''),
                item_data.get('terms', ''),
                item_data.get('created_at', 'CURRENT_TIMESTAMP')
            ))
            
            invoice_id = self.cursor.lastrowid
            
            # 還原發票項目
            for item in item_data.get('items', []):
                self.cursor.execute("""
                    INSERT INTO invoice_items (
                        invoice_id, product_id, description, 
                        quantity, unit_price, discount, amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity'],
                    item['unit_price'],
                    item['discount'],
                    item['amount']
                ))
            
            return True
        except Exception as e:
            system_logger.error(f"還原發票錯誤: {str(e)}")
            return False
    
    def _restore_delivery_note(self, item_data):
        """還原送貨單"""
        try:
            # 重新插入送貨單主表
            self.cursor.execute("""
                INSERT INTO delivery_notes (
                    delivery_no, customer_id, date, status, 
                    invoice_id, quotation_id, delivery_address, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item_data['delivery_no'],
                item_data['customer_id'],
                item_data['date'],
                item_data['status'],
                item_data.get('invoice_id'),
                item_data.get('quotation_id'),
                item_data['delivery_address'],
                item_data.get('created_at', 'CURRENT_TIMESTAMP')
            ))
            
            delivery_id = self.cursor.lastrowid
            
            # 還原送貨單項目
            for item in item_data.get('items', []):
                self.cursor.execute("""
                    INSERT INTO delivery_items (
                        delivery_id, product_id, description, 
                        quantity
                    ) VALUES (?, ?, ?, ?)
                """, (
                    delivery_id,
                    item.get('product_id'),
                    item['description'],
                    item['quantity']
                ))
            
            return True
        except Exception as e:
            system_logger.error(f"還原送貨單錯誤: {str(e)}")
            return False
    
    def _restore_customer(self, item_data):
        """還原客戶"""
        try:
            self.cursor.execute("""
                INSERT INTO customers (
                    name, contact_person, phone, email, address, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                item_data['name'],
                item_data['contact_person'],
                item_data['phone'],
                item_data['email'],
                item_data['address'],
                item_data.get('created_at', 'CURRENT_TIMESTAMP')
            ))
            return True
        except Exception as e:
            system_logger.error(f"還原客戶錯誤: {str(e)}")
            return False
    
    def _restore_product(self, item_data):
        """還原產品"""
        try:
            self.cursor.execute("""
                INSERT INTO products (
                    code, name, specification, unit, price, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                item_data['code'],
                item_data['name'],
                item_data['specification'],
                item_data['unit'],
                item_data['price'],
                item_data.get('created_at', 'CURRENT_TIMESTAMP')
            ))
            return True
        except Exception as e:
            system_logger.error(f"還原產品錯誤: {str(e)}")
            return False
 