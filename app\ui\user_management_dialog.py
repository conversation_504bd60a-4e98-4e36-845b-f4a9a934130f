from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
                           QLineEdit, QPushButton, QComboBox, QMessageBox,
                           QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager

class UserManagementDialog(QDialog):
    """用戶管理對話框"""
    
    def __init__(self, parent=None, db_manager=None, current_user=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user  # 當前登錄用戶
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        
        self.init_ui()
        self.load_users()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(self.lang_manager.get_text('user_management'))
        self.setMinimumSize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 搜索和按鈕區域
        top_layout = QHBoxLayout()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(self.lang_manager.get_text('search_users'))
        self.search_edit.textChanged.connect(self.filter_users)
        top_layout.addWidget(self.search_edit)
        
        # 添加用戶按鈕
        self.add_user_btn = QPushButton(self.lang_manager.get_text('add_user'))
        self.add_user_btn.setIcon(self.icon_manager.get_icon('add'))
        self.add_user_btn.clicked.connect(self.add_user)
        top_layout.addWidget(self.add_user_btn)
        
        # 編輯用戶按鈕
        self.edit_user_btn = QPushButton(self.lang_manager.get_text('edit_user'))
        self.edit_user_btn.setIcon(self.icon_manager.get_icon('edit'))
        self.edit_user_btn.clicked.connect(self.edit_user)
        top_layout.addWidget(self.edit_user_btn)
        
        # 刪除用戶按鈕
        self.delete_user_btn = QPushButton(self.lang_manager.get_text('delete_user'))
        self.delete_user_btn.setIcon(self.icon_manager.get_icon('delete'))
        self.delete_user_btn.clicked.connect(self.delete_user)
        top_layout.addWidget(self.delete_user_btn)
        
        main_layout.addLayout(top_layout)
        
        # 用戶表格
        self.users_table = QTableWidget(0, 6)  # 用戶名、姓名、角色、狀態、最後登錄、創建時間
        self.users_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('username'),
            self.lang_manager.get_text('full_name'),
            self.lang_manager.get_text('role'),
            self.lang_manager.get_text('status'),
            self.lang_manager.get_text('last_login'),
            self.lang_manager.get_text('created_at')
        ])
        
        # 設置表格屬性
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.users_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.setAlternatingRowColors(True)
        
        main_layout.addWidget(self.users_table)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 關閉按鈕
        self.close_button = QPushButton(self.lang_manager.get_text('close'))
        self.close_button.setIcon(self.icon_manager.get_icon('cancel'))
        self.close_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
        
        # 根據當前用戶角色限制操作
        self.update_ui_permissions()
    
    def update_ui_permissions(self):
        """根據當前用戶角色更新界面權限"""
        if not self.current_user or self.current_user.get('role') != 'admin':
            self.add_user_btn.setEnabled(False)
            self.delete_user_btn.setEnabled(False)
            
            if self.current_user and self.current_user.get('role') == 'manager':
                # 高級管理可以編輯基本用戶
                self.edit_user_btn.setEnabled(True)
            else:
                self.edit_user_btn.setEnabled(False)
    
    def load_users(self):
        """加載用戶列表到表格"""
        if not self.db_manager:
            return
            
        users = self.db_manager.get_users()
        
        # 清空表格
        self.users_table.setRowCount(0)
        
        # 填充表格
        for user in users:
            row = self.users_table.rowCount()
            self.users_table.insertRow(row)
            
            # 用戶名
            self.users_table.setItem(row, 0, QTableWidgetItem(user.get('username', '')))
            
            # 姓名
            self.users_table.setItem(row, 1, QTableWidgetItem(user.get('full_name', '')))
            
            # 角色
            role = user.get('role', '')
            role_text = self.get_role_display_text(role)
            self.users_table.setItem(row, 2, QTableWidgetItem(role_text))
            
            # 狀態
            status = user.get('status', '')
            status_text = self.lang_manager.get_text('active') if status == 'active' else self.lang_manager.get_text('inactive')
            self.users_table.setItem(row, 3, QTableWidgetItem(status_text))
            
            # 最後登錄
            last_login = user.get('last_login', '') or ''
            if last_login:
                last_login = last_login.split('T')[0]  # 只顯示日期部分
            self.users_table.setItem(row, 4, QTableWidgetItem(last_login))
            
            # 創建時間
            created_at = user.get('created_at', '') or ''
            if created_at:
                created_at = created_at.split('T')[0]  # 只顯示日期部分
            self.users_table.setItem(row, 5, QTableWidgetItem(created_at))
            
            # 存儲用戶ID作為隱藏數據
            self.users_table.item(row, 0).setData(Qt.UserRole, user.get('id'))
    
    def get_role_display_text(self, role):
        """獲取角色的顯示文本"""
        if role == 'admin':
            return self.lang_manager.get_text('role_admin')
        elif role == 'manager':
            return self.lang_manager.get_text('role_manager')
        else:
            return self.lang_manager.get_text('role_user')
    
    def filter_users(self):
        """過濾用戶列表"""
        search_text = self.search_edit.text().lower()
        
        for row in range(self.users_table.rowCount()):
            match = False
            
            # 檢查用戶名和姓名
            username = self.users_table.item(row, 0).text().lower()
            full_name = self.users_table.item(row, 1).text().lower()
            
            if search_text in username or search_text in full_name:
                match = True
                
            self.users_table.setRowHidden(row, not match)
    
    def add_user(self):
        """添加用戶"""
        if self.current_user and self.current_user.get('role') != 'admin':
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('admin_permission_required')
            )
            return
            
        dialog = UserDialog(self, self.db_manager)
        if dialog.exec_():
            self.load_users()  # 重新加載用戶列表
    
    def edit_user(self):
        """編輯用戶"""
        selected_rows = self.users_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('select_user_first')
            )
            return
            
        # 獲取選中的用戶ID
        row = selected_rows[0].row()
        user_id = self.users_table.item(row, 0).data(Qt.UserRole)
        
        # 獲取用戶角色
        user_role = self.db_manager.get_user(user_id).get('role')
        
        # 檢查權限
        if self.current_user:
            current_role = self.current_user.get('role')
            
            if current_role != 'admin' and (user_role == 'admin' or user_role == 'manager'):
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('warning'),
                    self.lang_manager.get_text('cant_edit_higher_role')
                )
                return
        
        # 打開編輯對話框
        dialog = UserDialog(self, self.db_manager, user_id)
        if dialog.exec_():
            self.load_users()  # 重新加載用戶列表
    
    def delete_user(self):
        """刪除用戶"""
        if self.current_user and self.current_user.get('role') != 'admin':
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('admin_permission_required')
            )
            return
            
        selected_rows = self.users_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('select_user_first')
            )
            return
            
        # 獲取選中的用戶資訊
        row = selected_rows[0].row()
        user_id = self.users_table.item(row, 0).data(Qt.UserRole)
        username = self.users_table.item(row, 0).text()
        
        # 確認刪除
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('confirm_delete_user').format(username=username),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
            
        # 執行刪除
        success, message = self.db_manager.delete_user(user_id)
        
        if success:
            QMessageBox.information(
                self,
                self.lang_manager.get_text('success'),
                message
            )
            self.load_users()  # 重新加載用戶列表
        else:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                message
            )
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        
        self.setWindowTitle(self.lang_manager.get_text('user_management'))
        self.search_edit.setPlaceholderText(self.lang_manager.get_text('search_users'))
        
        self.add_user_btn.setText(self.lang_manager.get_text('add_user'))
        self.edit_user_btn.setText(self.lang_manager.get_text('edit_user'))
        self.delete_user_btn.setText(self.lang_manager.get_text('delete_user'))
        self.close_button.setText(self.lang_manager.get_text('close'))
        
        # 更新表頭
        self.users_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('username'),
            self.lang_manager.get_text('full_name'),
            self.lang_manager.get_text('role'),
            self.lang_manager.get_text('status'),
            self.lang_manager.get_text('last_login'),
            self.lang_manager.get_text('created_at')
        ])
        
        # 重新加載用戶列表以更新角色和狀態的翻譯
        self.load_users()


class UserDialog(QDialog):
    """用戶添加/編輯對話框"""
    
    def __init__(self, parent=None, db_manager=None, user_id=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id  # 如果提供，則為編輯模式
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.user_data = None
        
        # 如果是編輯模式，加載用戶數據
        if self.user_id:
            self.user_data = self.db_manager.get_user(self.user_id)
        
        self.init_ui()
        
        # 如果是編輯模式，填充表單
        if self.user_data:
            self.fill_form()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(self.lang_manager.get_text('add_user') if not self.user_id else self.lang_manager.get_text('edit_user'))
        self.setMinimumWidth(400)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 表單布局
        form_layout = QGridLayout()
        form_layout.setVerticalSpacing(10)
        form_layout.setHorizontalSpacing(10)
        
        # 用戶名
        username_label = QLabel(self.lang_manager.get_text('username'))
        self.username_edit = QLineEdit()
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_edit, 0, 1)
        
        # 姓名
        fullname_label = QLabel(self.lang_manager.get_text('full_name'))
        self.fullname_edit = QLineEdit()
        form_layout.addWidget(fullname_label, 1, 0)
        form_layout.addWidget(self.fullname_edit, 1, 1)
        
        # 密碼
        password_label = QLabel(self.lang_manager.get_text('password'))
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        if self.user_id:  # 編輯模式下密碼為空表示不變更
            self.password_edit.setPlaceholderText(self.lang_manager.get_text('leave_blank_unchanged'))
        form_layout.addWidget(password_label, 2, 0)
        form_layout.addWidget(self.password_edit, 2, 1)
        
        # 確認密碼
        confirm_label = QLabel(self.lang_manager.get_text('confirm_password'))
        self.confirm_edit = QLineEdit()
        self.confirm_edit.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(confirm_label, 3, 0)
        form_layout.addWidget(self.confirm_edit, 3, 1)
        
        # 角色
        role_label = QLabel(self.lang_manager.get_text('role'))
        self.role_combo = QComboBox()
        self.role_combo.addItem(self.lang_manager.get_text('role_admin'), 'admin')
        self.role_combo.addItem(self.lang_manager.get_text('role_manager'), 'manager')
        self.role_combo.addItem(self.lang_manager.get_text('role_user'), 'user')
        form_layout.addWidget(role_label, 4, 0)
        form_layout.addWidget(self.role_combo, 4, 1)
        
        # 狀態
        status_label = QLabel(self.lang_manager.get_text('status'))
        self.status_combo = QComboBox()
        self.status_combo.addItem(self.lang_manager.get_text('active'), 'active')
        self.status_combo.addItem(self.lang_manager.get_text('inactive'), 'inactive')
        form_layout.addWidget(status_label, 5, 0)
        form_layout.addWidget(self.status_combo, 5, 1)
        
        main_layout.addLayout(form_layout)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton(self.lang_manager.get_text('save'))
        self.save_button.setIcon(self.icon_manager.get_icon('save'))
        self.save_button.clicked.connect(self.save_user)
        
        self.cancel_button = QPushButton(self.lang_manager.get_text('cancel'))
        self.cancel_button.setIcon(self.icon_manager.get_icon('cancel'))
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        # 設置焦點
        self.username_edit.setFocus()
    
    def fill_form(self):
        """填充表單（編輯模式）"""
        if not self.user_data:
            return
            
        self.username_edit.setText(self.user_data.get('username', ''))
        self.fullname_edit.setText(self.user_data.get('full_name', ''))
        
        # 設置角色
        role = self.user_data.get('role', 'user')
        for i in range(self.role_combo.count()):
            if self.role_combo.itemData(i) == role:
                self.role_combo.setCurrentIndex(i)
                break
                
        # 設置狀態
        status = self.user_data.get('status', 'active')
        index = 0 if status == 'active' else 1
        self.status_combo.setCurrentIndex(index)
        
        # 如果是 admin 用戶，限制修改
        if self.user_data.get('username') == 'admin':
            self.username_edit.setReadOnly(True)
            self.role_combo.setEnabled(False)
    
    def save_user(self):
        """保存用戶"""
        username = self.username_edit.text().strip()
        fullname = self.fullname_edit.text().strip()
        password = self.password_edit.text()
        confirm = self.confirm_edit.text()
        role = self.role_combo.currentData()
        status = self.status_combo.currentData()
        
        # 驗證輸入
        if not username:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('username_required')
            )
            self.username_edit.setFocus()
            return
            
        # 新增模式或密碼已輸入時，檢查密碼匹配
        if not self.user_id or password:
            if not password:
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('warning'),
                    self.lang_manager.get_text('password_required')
                )
                self.password_edit.setFocus()
                return
                
            if password != confirm:
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('warning'),
                    self.lang_manager.get_text('password_mismatch')
                )
                self.confirm_edit.setFocus()
                return
        
        # 準備用戶數據
        user_data = {
            'username': username,
            'full_name': fullname,
            'role': role,
            'status': status
        }
        
        # 只有在新增模式或密碼已輸入時，才包含密碼
        if not self.user_id or password:
            user_data['password'] = password
        
        # 保存用戶
        if self.user_id:
            # 編輯模式
            success, message = self.db_manager.update_user(self.user_id, user_data)
        else:
            # 新增模式
            success, message = self.db_manager.add_user(user_data)
        
        if success:
            QMessageBox.information(
                self,
                self.lang_manager.get_text('success'),
                message
            )
            self.accept()  # 關閉對話框並返回接受
        else:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                message
            )
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        
        self.setWindowTitle(self.lang_manager.get_text('add_user') if not self.user_id else self.lang_manager.get_text('edit_user'))
        
        # 更新表單標籤
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.layout() and isinstance(item.layout(), QGridLayout):
                grid_layout = item.layout()
                
                # 遍歷網格佈局尋找標籤
                for row in range(grid_layout.rowCount()):
                    label_item = grid_layout.itemAtPosition(row, 0)
                    if label_item and label_item.widget():
                        label = label_item.widget()
                        if isinstance(label, QLabel):
                            if 'username' in label.text().lower():
                                label.setText(self.lang_manager.get_text('username'))
                            elif 'full name' in label.text().lower():
                                label.setText(self.lang_manager.get_text('full_name'))
                            elif 'password' in label.text().lower() and 'confirm' not in label.text().lower():
                                label.setText(self.lang_manager.get_text('password'))
                            elif 'confirm' in label.text().lower():
                                label.setText(self.lang_manager.get_text('confirm_password'))
                            elif 'role' in label.text().lower():
                                label.setText(self.lang_manager.get_text('role'))
                            elif 'status' in label.text().lower():
                                label.setText(self.lang_manager.get_text('status'))
                                
        # 更新下拉列表
        self.role_combo.clear()
        self.role_combo.addItem(self.lang_manager.get_text('role_admin'), 'admin')
        self.role_combo.addItem(self.lang_manager.get_text('role_manager'), 'manager')
        self.role_combo.addItem(self.lang_manager.get_text('role_user'), 'user')
        
        self.status_combo.clear()
        self.status_combo.addItem(self.lang_manager.get_text('active'), 'active')
        self.status_combo.addItem(self.lang_manager.get_text('inactive'), 'inactive')
        
        # 更新按鈕
        self.save_button.setText(self.lang_manager.get_text('save'))
        self.cancel_button.setText(self.lang_manager.get_text('cancel')) 