#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import configparser
from datetime import datetime
from typing import Dict, List, Optional, Any

# 檢查並導入ReportLab
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.units import mm
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.enums import TA_LEFT, TA_RIGHT, TA_CENTER
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError as e:
    REPORTLAB_AVAILABLE = False
    print(f"ReportLab not available: {e}")

# 導入logger（如果可用）
try:
    from app.utils.logger import system_logger
except ImportError:
    # 創建簡單的logger替代
    class SimpleLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    system_logger = SimpleLogger()

class PDFGeneratorSimple:
    """簡單的PDF生成器 - 使用ReportLab，無複雜依賴
    
    專為Windows環境優化，避免WeasyPrint的複雜依賴問題
    """
    
    def __init__(self):
        """初始化PDF生成器"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab is required but not installed. Please run: pip install reportlab")
        
        # 讀取配置
        self.config = configparser.ConfigParser()
        try:
            self.config.read('config/settings.ini', encoding='utf-8')
        except:
            # 如果配置文件不存在，使用默認值
            pass
        
        # 創建輸出目錄
        os.makedirs('output', exist_ok=True)
        
        # 頁面設置
        self.page_width, self.page_height = A4
        self.margin = 15 * mm
        self.content_width = self.page_width - 2 * self.margin
        
        # 樣式設置
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
        system_logger.info("簡單PDF生成器初始化完成")
    
    def _setup_custom_styles(self):
        """設置自定義樣式"""
        # 公司名稱樣式（中文）
        self.styles.add(ParagraphStyle(
            name='CompanyNameCN',
            parent=self.styles['Normal'],
            fontName='Helvetica-Bold',
            fontSize=14,
            leading=16,
            alignment=TA_LEFT
        ))
        
        # 公司名稱樣式（英文）
        self.styles.add(ParagraphStyle(
            name='CompanyNameEN',
            parent=self.styles['Normal'],
            fontName='Helvetica-Bold',
            fontSize=12,
            leading=14,
            alignment=TA_LEFT
        ))
        
        # 文檔標題樣式
        self.styles.add(ParagraphStyle(
            name='DocumentTitle',
            parent=self.styles['Normal'],
            fontName='Helvetica-Bold',
            fontSize=24,
            leading=28,
            alignment=TA_CENTER,
            spaceAfter=20
        ))
        
        # 客戶信息樣式
        self.styles.add(ParagraphStyle(
            name='CustomerInfo',
            parent=self.styles['Normal'],
            fontName='Helvetica',
            fontSize=10,
            leading=12,
            alignment=TA_RIGHT
        ))
        
        # 項目描述樣式
        self.styles.add(ParagraphStyle(
            name='ItemDescription',
            parent=self.styles['Normal'],
            fontName='Helvetica',
            fontSize=9,
            leading=11,
            alignment=TA_LEFT
        ))
        
        # 條款樣式
        self.styles.add(ParagraphStyle(
            name='Terms',
            parent=self.styles['Normal'],
            fontName='Helvetica',
            fontSize=9,
            leading=11,
            alignment=TA_LEFT
        ))
    
    def generate_quotation_pdf(self, quotation_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成報價單PDF"""
        try:
            if output_path is None:
                quotation_no = quotation_data.get('quotation_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/Quotation_Simple_{quotation_no}.pdf"
            
            # 創建PDF文檔
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            # 構建文檔內容
            story = []
            
            # 添加頁首
            story.extend(self._build_header(quotation_data))
            
            # 添加文檔信息
            story.extend(self._build_document_info(quotation_data, 'QUOTATION'))
            
            # 添加項目列表
            story.extend(self._build_items_section(quotation_data.get('items', [])))
            
            # 添加總計和條款
            story.extend(self._build_summary_section(quotation_data))
            
            # 生成PDF
            doc.build(story)
            
            system_logger.info(f"成功生成報價單PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成報價單PDF時出錯: {str(e)}")
            raise
    
    def generate_invoice_pdf(self, invoice_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成發票PDF"""
        try:
            if output_path is None:
                invoice_no = invoice_data.get('invoice_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/Invoice_Simple_{invoice_no}.pdf"
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            story = []
            story.extend(self._build_header(invoice_data))
            story.extend(self._build_document_info(invoice_data, 'INVOICE'))
            story.extend(self._build_items_section(invoice_data.get('items', [])))
            story.extend(self._build_summary_section(invoice_data))
            
            doc.build(story)
            
            system_logger.info(f"成功生成發票PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成發票PDF時出錯: {str(e)}")
            raise
    
    def generate_delivery_note_pdf(self, delivery_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成送貨單PDF"""
        try:
            if output_path is None:
                delivery_no = delivery_data.get('delivery_note_no', 'UNKNOWN').replace('/', '_')
                output_path = f"output/DeliveryNote_Simple_{delivery_no}.pdf"
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            story = []
            story.extend(self._build_header(delivery_data))
            story.extend(self._build_document_info(delivery_data, 'DELIVERY NOTE'))
            story.extend(self._build_items_section(delivery_data.get('items', [])))
            story.extend(self._build_summary_section(delivery_data))
            
            doc.build(story)
            
            system_logger.info(f"成功生成送貨單PDF: {output_path}")
            return output_path
            
        except Exception as e:
            system_logger.error(f"生成送貨單PDF時出錯: {str(e)}")
            raise
    
    def _build_header(self, data: Dict[str, Any]) -> List:
        """構建頁首"""
        story = []
        
        # 獲取公司信息
        company_name_cn = self.config.get('CompanyInfo', 'name_cn', fallback='蒼藍工程公司')
        company_name_en = self.config.get('CompanyInfo', 'name_en', fallback='H.B ENGINEERING COMPANY')
        company_nature = self.config.get('CompanyInfo', 'nature_en', fallback='Electrical Engineering')
        
        # 創建頁首表格
        header_data = [
            [
                Paragraph(f"{company_name_cn}<br/>{company_name_en}<br/><i>{company_nature}</i>", self.styles['CompanyNameEN']),
                Paragraph(f"<b>CUSTOMER INFORMATION.</b><br/>{data.get('customer_name', '')}<br/>{data.get('contact_person', '')}<br/>{data.get('address', '')}<br/>Tel: {data.get('phone', '')} | Fax: {data.get('fax', '')}", self.styles['CustomerInfo'])
            ]
        ]
        
        header_table = Table(header_data, colWidths=[self.content_width/2, self.content_width/2])
        header_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LINEBELOW', (0, 0), (-1, -1), 1, colors.black),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(header_table)
        story.append(Spacer(1, 12))
        
        return story
    
    def _build_document_info(self, data: Dict[str, Any], doc_type: str) -> List:
        """構建文檔信息"""
        story = []
        
        # 獲取公司電話傳真
        phone = self.config.get('CompanyInfo', 'phone', fallback='60173180')
        fax = self.config.get('CompanyInfo', 'fax', fallback='60173180')
        
        # 文檔編號
        doc_no = data.get('quotation_no', data.get('invoice_no', data.get('delivery_note_no', '')))
        
        # 信息表格
        info_data = [
            ['DATE:', data.get('date', datetime.now().strftime('%B %d, %Y')).upper(), 'TEL:', phone],
            ['NO.:', doc_no, 'FAX:', fax],
            ['SALES:', data.get('sales_person', 'SALES TEAM'), '', '']
        ]
        
        info_table = Table(info_data, colWidths=[self.content_width*0.12, self.content_width*0.38, self.content_width*0.12, self.content_width*0.38])
        info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 15))
        
        # 文檔標題
        story.append(Paragraph(doc_type, self.styles['DocumentTitle']))
        
        # 合約信息
        contract_info = f"Contract No: {data.get('contract_no', 'HB-ENG-2025-001')}<br/><b>{data.get('contract_description', 'ELECTRICAL INSTALLATION AND MAINTENANCE CONTRACT')}</b>"
        story.append(Paragraph(contract_info, self.styles['Normal']))
        story.append(Spacer(1, 12))
        
        return story
    
    def _build_items_section(self, items: List[Dict]) -> List:
        """構建項目列表 - 參考圖片的表格格式"""
        story = []
        
        # 創建表格標題行
        table_data = [
            ['項目', '描述', '數量', '單價', '金額(HKD)']
        ]
        
        # 添加項目數據
        for i, item in enumerate(items, 1):
            model = item.get('model', item.get('product_name', 'N/A'))
            description = item.get('description', '')
            quantity = float(item.get('quantity', 1))
            unit_price = float(item.get('unit_price', 0))
            discount = float(item.get('discount', 0))
            
            # 修復金額計算
            if item.get('amount'):
                amount = float(item.get('amount'))
            else:
                amount = quantity * unit_price * (1 - discount / 100)
            
            # 組合項目信息
            item_info = f"{i}"
            desc_info = f"{model}"
            if description:
                desc_info += f"\n{description}"
            
            table_data.append([
                item_info,
                desc_info,
                f"{quantity:.1f}",
                f"${unit_price:,.2f}",
                f"${amount:,.2f}"
            ])
        
        # 創建表格
        items_table = Table(table_data, colWidths=[
            self.content_width * 0.08,  # 項目編號
            self.content_width * 0.42,  # 描述
            self.content_width * 0.12,  # 數量
            self.content_width * 0.19,  # 單價
            self.content_width * 0.19   # 金額
        ])
        
        # 設置表格樣式 - 參考圖片的簡潔風格
        items_table.setStyle(TableStyle([
            # 標題行樣式
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            
            # 數據行樣式
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # 項目編號居中
            ('ALIGN', (1, 1), (1, -1), 'LEFT'),    # 描述左對齊
            ('ALIGN', (2, 1), (2, -1), 'CENTER'),  # 數量居中
            ('ALIGN', (3, 1), (-1, -1), 'RIGHT'),  # 單價和金額右對齊
            
            # 邊框樣式 - 簡潔的線條
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('LINEBELOW', (0, 0), (-1, 0), 1, colors.black),  # 標題行下方粗線
            
            # 內邊距
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            
            # 垂直對齊
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(items_table)
        story.append(Spacer(1, 15))
        
        return story
    
    def _build_summary_section(self, data: Dict[str, Any]) -> List:
        """構建總計和條款部分 - 參考圖片的簡潔風格"""
        story = []
        
        # 總計 - 參考圖片的樣式
        total_amount = float(data.get('total_amount', 0))
        
        # 創建總計表格
        total_data = [['總計 TOTAL:', f'${total_amount:,.2f}']]
        total_table = Table(total_data, colWidths=[40*mm, 50*mm])
        total_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('LINEABOVE', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        # 右對齊總計
        total_wrapper = Table([[total_table]], colWidths=[self.content_width])
        total_wrapper.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'RIGHT'),
        ]))
        
        story.append(total_wrapper)
        story.append(Spacer(1, 20))
        
        # 付款條件 - 參考圖片的簡潔樣式
        payment_terms = data.get('terms', '付款條件 Payment Terms:')
        story.append(Paragraph(f"<b>付款條件 Payment Terms:</b> {payment_terms}", self.styles['Terms']))
        story.append(Spacer(1, 30))
        
        # 簽名區域 - 參考圖片的底部佈局
        company_name_en = self.config.get('CompanyInfo', 'name_en', fallback='H.B ENGINEERING COMPANY')
        customer_name = data.get('customer_name', '')
        
        # 創建簽名表格 - 參考圖片的格式
        signature_data = [
            [f'{company_name_en}', '', f'{customer_name}'],
            ['授權簽署 Authorized Signature', '', '客戶簽署及公司印章'],
            ['姓名 Name:', '', '姓名 Name:'],
            ['日期 Date:', '', '日期 Date:']
        ]
        
        signature_table = Table(signature_data, colWidths=[
            self.content_width * 0.4,   # 左側公司
            self.content_width * 0.2,   # 中間空白
            self.content_width * 0.4    # 右側客戶
        ])
        
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            # 添加底部線條
            ('LINEBELOW', (0, 0), (0, 0), 0.5, colors.black),
            ('LINEBELOW', (2, 0), (2, 0), 0.5, colors.black),
        ]))
        
        story.append(signature_table)
        story.append(Spacer(1, 10))
        
        # 底部說明文字 - 參考圖片
        validity_text = f"此報價單有效期為30天 This quotation is valid for 30 days"
        story.append(Paragraph(validity_text, self.styles['Terms']))
        
        # 頁碼 - 參考圖片右下角
        page_text = "頁數 Page 1/1"
        page_para = Paragraph(f'<para align="right">{page_text}</para>', self.styles['Terms'])
        story.append(page_para)
        
        return story 