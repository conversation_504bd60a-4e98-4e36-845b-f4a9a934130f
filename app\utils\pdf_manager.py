#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(os.path.dirname(current_dir)))

from app.utils.logger import system_logger

class PDFManager:
    """PDF管理器 - 統一的PDF生成接口，自動選擇最佳生成器"""
    
    def __init__(self):
        """初始化PDF管理器"""
        self.pdf_generator = None
        self.generator_type = None
        self._initialize_generator()
        
        system_logger.info("PDF管理器初始化完成")
    
    def _initialize_generator(self):
        """初始化PDF生成器 - 按優先級嘗試不同的生成器"""
        
        # 第一優先級：WeasyPrint（最專業）
        try:
            from app.utils.pdf_generator_weasy import PDFGeneratorWeasy
            self.pdf_generator = PDFGeneratorWeasy()
            self.generator_type = "WeasyPrint"
            system_logger.info("✅ 使用WeasyPrint PDF生成器（最佳選擇）")
            return
        except ImportError as e:
            system_logger.info(f"⚠️  WeasyPrint不可用: {e}")
        except Exception as e:
            system_logger.warning(f"⚠️  WeasyPrint初始化失敗: {e}")
        
        # 第二優先級：簡單ReportLab生成器（穩定可靠）
        try:
            from app.utils.pdf_generator_simple import PDFGeneratorSimple
            self.pdf_generator = PDFGeneratorSimple()
            self.generator_type = "ReportLab-Simple"
            system_logger.info("✅ 使用簡單ReportLab PDF生成器（推薦選擇）")
            return
        except ImportError as e:
            system_logger.info(f"⚠️  ReportLab不可用: {e}")
        except Exception as e:
            system_logger.warning(f"⚠️  簡單PDF生成器初始化失敗: {e}")
        
        # 第三優先級：原有ReportLab生成器（如果存在）
        try:
            from app.utils.pdf_generator import PDFGenerator
            self.pdf_generator = PDFGenerator()
            self.generator_type = "ReportLab-Legacy"
            system_logger.info("✅ 使用傳統ReportLab PDF生成器（備用選擇）")
            return
        except ImportError as e:
            system_logger.info(f"⚠️  傳統ReportLab生成器不可用: {e}")
        except Exception as e:
            system_logger.warning(f"⚠️  傳統PDF生成器初始化失敗: {e}")
        
        # 如果所有生成器都不可用
        system_logger.error("❌ 沒有可用的PDF生成器")
        raise ImportError(
            "沒有可用的PDF生成器。請安裝以下任一依賴：\n"
            "1. WeasyPrint (推薦): pip install weasyprint\n"
            "2. ReportLab (備用): pip install reportlab"
        )
    
    def generate_quotation_pdf(self, quotation_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成報價單PDF
        
        Args:
            quotation_data: 報價單數據
            output_path: 輸出文件路徑
            
        Returns:
            生成的PDF文件路徑
        """
        try:
            if not self.pdf_generator:
                raise RuntimeError("PDF生成器未初始化")
            
            # 確保數據完整性
            quotation_data = self._validate_quotation_data(quotation_data)
            
            # 生成PDF
            pdf_path = self.pdf_generator.generate_quotation_pdf(quotation_data, output_path)
            
            system_logger.info(f"✅ 成功生成報價單PDF: {pdf_path} (使用{self.generator_type})")
            return pdf_path
            
        except Exception as e:
            system_logger.error(f"❌ 生成報價單PDF失敗: {str(e)}")
            raise
    
    def generate_invoice_pdf(self, invoice_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成發票PDF
        
        Args:
            invoice_data: 發票數據
            output_path: 輸出文件路徑
            
        Returns:
            生成的PDF文件路徑
        """
        try:
            if not self.pdf_generator:
                raise RuntimeError("PDF生成器未初始化")
            
            # 確保數據完整性
            invoice_data = self._validate_invoice_data(invoice_data)
            
            # 生成PDF
            pdf_path = self.pdf_generator.generate_invoice_pdf(invoice_data, output_path)
            
            system_logger.info(f"✅ 成功生成發票PDF: {pdf_path} (使用{self.generator_type})")
            return pdf_path
            
        except Exception as e:
            system_logger.error(f"❌ 生成發票PDF失敗: {str(e)}")
            raise
    
    def generate_delivery_note_pdf(self, delivery_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """生成送貨單PDF
        
        Args:
            delivery_data: 送貨單數據
            output_path: 輸出文件路徑
            
        Returns:
            生成的PDF文件路徑
        """
        try:
            if not self.pdf_generator:
                raise RuntimeError("PDF生成器未初始化")
            
            # 確保數據完整性
            delivery_data = self._validate_delivery_data(delivery_data)
            
            # 生成PDF
            pdf_path = self.pdf_generator.generate_delivery_note_pdf(delivery_data, output_path)
            
            system_logger.info(f"✅ 成功生成送貨單PDF: {pdf_path} (使用{self.generator_type})")
            return pdf_path
            
        except Exception as e:
            system_logger.error(f"❌ 生成送貨單PDF失敗: {str(e)}")
            raise
    
    def _validate_quotation_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """驗證和補充報價單數據"""
        validated_data = data.copy()
        
        # 必需字段的默認值
        defaults = {
            'quotation_no': f'Q{datetime.now().strftime("%Y%m%d%H%M")}',
            'date': datetime.now().strftime('%B %d, %Y').upper(),
            'customer_name': 'CUSTOMER NAME',
            'contact_person': 'CONTACT PERSON',
            'address': 'CUSTOMER ADDRESS',
            'phone': '+852 0000 0000',
            'fax': '+852 0000 0000',
            'sales_person': 'SALES TEAM',
            'contract_no': 'HB-ENG-2025-001',
            'contract_description': 'ELECTRICAL INSTALLATION AND MAINTENANCE CONTRACT',
            'items': [],
            'total_amount': 0.0,
            'valid_until': 'MAY 17, 25',
            'terms': '30% DEPOSIT, 40% C.O.D., 30% AFTER T & C',
            'delivery_terms': 'APPROX. 4 WEEKS',
            'pricing_terms': 'C.I.F. HONG KONG'
        }
        
        # 應用默認值
        for key, default_value in defaults.items():
            if key not in validated_data or validated_data[key] is None:
                validated_data[key] = default_value
        
        # 驗證項目數據
        if validated_data['items']:
            validated_items = []
            for item in validated_data['items']:
                validated_item = {
                    'item_no': item.get('item_no', 1),
                    'model': item.get('model', item.get('product_name', 'N/A')),
                    'description': item.get('description', ''),
                    'quantity': float(item.get('quantity', 1)),
                    'unit': item.get('unit', 'SET'),
                    'unit_price': float(item.get('unit_price', 0)),
                    'discount': float(item.get('discount', 0)),
                    'amount': float(item.get('amount', 0))
                }
                
                # 如果沒有提供amount，則計算
                if validated_item['amount'] == 0:
                    validated_item['amount'] = (validated_item['quantity'] * 
                                              validated_item['unit_price'] * 
                                              (1 - validated_item['discount'] / 100))
                
                validated_items.append(validated_item)
            
            validated_data['items'] = validated_items
            
            # 如果沒有提供總金額，則計算
            if validated_data['total_amount'] == 0:
                validated_data['total_amount'] = sum(item['amount'] for item in validated_items)
        
        return validated_data
    
    def _validate_invoice_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """驗證和補充發票數據"""
        validated_data = self._validate_quotation_data(data)
        
        # 發票特定的默認值
        if 'invoice_no' not in validated_data:
            validated_data['invoice_no'] = validated_data.get('quotation_no', f'INV{datetime.now().strftime("%Y%m%d%H%M")}')
        
        return validated_data
    
    def _validate_delivery_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """驗證和補充送貨單數據"""
        validated_data = self._validate_quotation_data(data)
        
        # 送貨單特定的默認值
        if 'delivery_note_no' not in validated_data:
            validated_data['delivery_note_no'] = validated_data.get('quotation_no', f'DN{datetime.now().strftime("%Y%m%d%H%M")}')
        
        return validated_data
    
    def get_generator_info(self) -> Dict[str, Any]:
        """獲取當前PDF生成器信息"""
        return {
            'type': self.generator_type,
            'available': self.pdf_generator is not None,
            'class': self.pdf_generator.__class__.__name__ if self.pdf_generator else None,
            'description': self._get_generator_description()
        }
    
    def _get_generator_description(self) -> str:
        """獲取生成器描述"""
        descriptions = {
            'WeasyPrint': '專業HTML/CSS到PDF轉換，最佳輸出質量',
            'ReportLab-Simple': '簡化的ReportLab生成器，穩定可靠',
            'ReportLab-Legacy': '傳統ReportLab生成器，功能完整'
        }
        return descriptions.get(self.generator_type, '未知生成器')
    
    def get_available_generators(self) -> List[str]:
        """獲取所有可用的生成器列表"""
        available = []
        
        # 檢查WeasyPrint
        try:
            from app.utils.pdf_generator_weasy import PDFGeneratorWeasy
            available.append('WeasyPrint')
        except ImportError:
            pass
        
        # 檢查簡單ReportLab
        try:
            from app.utils.pdf_generator_simple import PDFGeneratorSimple
            available.append('ReportLab-Simple')
        except ImportError:
            pass
        
        # 檢查傳統ReportLab
        try:
            from app.utils.pdf_generator import PDFGenerator
            available.append('ReportLab-Legacy')
        except ImportError:
            pass
        
        return available
    
    def test_generation(self) -> bool:
        """測試PDF生成功能"""
        try:
            # 創建測試數據
            test_data = {
                'quotation_no': 'TEST-001',
                'date': 'TEST DATE',
                'customer_name': 'TEST CUSTOMER',
                'contact_person': 'TEST PERSON',
                'address': 'TEST ADDRESS',
                'phone': '+852 0000 0000',
                'fax': '+852 0000 0000',
                'sales_person': 'TEST SALES',
                'items': [
                    {
                        'item_no': 1,
                        'model': 'TEST-MODEL',
                        'description': 'Test item description for PDF generation testing',
                        'quantity': 1.0,
                        'unit': 'SET',
                        'unit_price': 1000.0,
                        'discount': 0,
                        'amount': 1000.0
                    }
                ],
                'total_amount': 1000.0
            }
            
            # 測試生成
            test_path = f"output/TEST_{self.generator_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf_path = self.generate_quotation_pdf(test_data, test_path)
            
            # 檢查文件是否存在
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path)
                system_logger.info(f"✅ 測試PDF生成成功: {pdf_path} ({file_size} bytes)")
                
                # 清理測試文件
                try:
                    os.remove(pdf_path)
                    system_logger.info("🗑️  測試文件已清理")
                except OSError as e:
                    system_logger.warning(f"無法清理測試文件 {pdf_path}: {str(e)}")
                    pass
                return True
            
            return False
            
        except Exception as e:
            system_logger.error(f"❌ PDF生成測試失敗: {str(e)}")
            return False
    
    def get_installation_guide(self) -> str:
        """獲取安裝指南"""
        if self.pdf_generator:
            return f"當前使用: {self.generator_type} - {self._get_generator_description()}"
        
        return """
PDF生成器安裝指南:

1. WeasyPrint (推薦 - 最佳質量):
   Windows: pip install weasyprint
   Linux: sudo apt-get install python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0
          pip install weasyprint
   macOS: brew install pango harfbuzz libffi && pip install weasyprint

2. ReportLab (備用 - 穩定可靠):
   所有平台: pip install reportlab

建議先嘗試安裝WeasyPrint，如果遇到問題再使用ReportLab。
        """ 