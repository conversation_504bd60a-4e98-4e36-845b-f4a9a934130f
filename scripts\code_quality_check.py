#!/usr/bin/env python3
"""
代碼質量檢查腳本
檢查代碼中的常見問題和改進建議
"""

import os
import re
import sys
from pathlib import Path

class CodeQualityChecker:
    """代碼質量檢查器"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.issues = []
        
    def check_print_statements(self):
        """檢查是否還有print語句未替換為日誌"""
        print("檢查print語句...")
        
        for py_file in self.project_root.rglob("*.py"):
            if "scripts" in str(py_file):  # 跳過腳本目錄
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                for line_num, line in enumerate(lines, 1):
                    if re.search(r'\bprint\s*\(', line) and not line.strip().startswith('#'):
                        self.issues.append({
                            'type': 'print_statement',
                            'file': py_file,
                            'line': line_num,
                            'content': line.strip(),
                            'severity': 'medium'
                        })
            except Exception as e:
                print(f"無法讀取文件 {py_file}: {e}")
    
    def check_exception_handling(self):
        """檢查異常處理"""
        print("檢查異常處理...")
        
        for py_file in self.project_root.rglob("*.py"):
            if "scripts" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                in_except_block = False
                for line_num, line in enumerate(lines, 1):
                    stripped = line.strip()
                    
                    # 檢查裸露的except語句
                    if re.match(r'except\s*:', stripped):
                        self.issues.append({
                            'type': 'bare_except',
                            'file': py_file,
                            'line': line_num,
                            'content': stripped,
                            'severity': 'high'
                        })
                    
                    # 檢查except塊中是否有pass語句
                    if stripped.startswith('except'):
                        in_except_block = True
                    elif in_except_block and stripped == 'pass':
                        self.issues.append({
                            'type': 'empty_except',
                            'file': py_file,
                            'line': line_num,
                            'content': stripped,
                            'severity': 'medium'
                        })
                        in_except_block = False
                    elif in_except_block and stripped and not stripped.startswith(' '):
                        in_except_block = False
                        
            except Exception as e:
                print(f"無法讀取文件 {py_file}: {e}")
    
    def check_imports(self):
        """檢查導入語句"""
        print("檢查導入語句...")
        
        for py_file in self.project_root.rglob("*.py"):
            if "scripts" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                import_lines = []
                for line_num, line in enumerate(lines, 1):
                    stripped = line.strip()
                    if stripped.startswith('import ') or stripped.startswith('from '):
                        import_lines.append((line_num, stripped))
                
                # 檢查是否有未使用的導入
                content = ''.join(lines)
                for line_num, import_line in import_lines:
                    if import_line.startswith('from '):
                        match = re.search(r'from .+ import (.+)', import_line)
                        if match:
                            imported_items = [item.strip() for item in match.group(1).split(',')]
                            for item in imported_items:
                                if item not in content.replace(import_line, ''):
                                    self.issues.append({
                                        'type': 'unused_import',
                                        'file': py_file,
                                        'line': line_num,
                                        'content': f"未使用的導入: {item}",
                                        'severity': 'low'
                                    })
                                    
            except Exception as e:
                print(f"無法讀取文件 {py_file}: {e}")
    
    def check_docstrings(self):
        """檢查文檔字符串"""
        print("檢查文檔字符串...")
        
        for py_file in self.project_root.rglob("*.py"):
            if "scripts" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                # 檢查類和函數是否有文檔字符串
                for line_num, line in enumerate(lines, 1):
                    stripped = line.strip()
                    if (stripped.startswith('def ') or stripped.startswith('class ')) and not stripped.startswith('def __'):
                        # 檢查下一行是否是文檔字符串
                        if line_num < len(lines):
                            next_line = lines[line_num].strip() if line_num < len(lines) else ""
                            if not (next_line.startswith('"""') or next_line.startswith("'''")):
                                self.issues.append({
                                    'type': 'missing_docstring',
                                    'file': py_file,
                                    'line': line_num,
                                    'content': stripped,
                                    'severity': 'low'
                                })
                                
            except Exception as e:
                print(f"無法讀取文件 {py_file}: {e}")
    
    def generate_report(self):
        """生成報告"""
        print("\n" + "="*60)
        print("代碼質量檢查報告")
        print("="*60)
        
        if not self.issues:
            print("✅ 未發現代碼質量問題！")
            return
        
        # 按嚴重程度分組
        high_issues = [i for i in self.issues if i['severity'] == 'high']
        medium_issues = [i for i in self.issues if i['severity'] == 'medium']
        low_issues = [i for i in self.issues if i['severity'] == 'low']
        
        print(f"總計發現 {len(self.issues)} 個問題：")
        print(f"  🔴 高優先級: {len(high_issues)}")
        print(f"  🟡 中優先級: {len(medium_issues)}")
        print(f"  🟢 低優先級: {len(low_issues)}")
        print()
        
        # 顯示高優先級問題
        if high_issues:
            print("🔴 高優先級問題：")
            for issue in high_issues:
                print(f"  📁 {issue['file']}")
                print(f"  📍 第{issue['line']}行: {issue['content']}")
                print(f"  ⚠️  {issue['type']}")
                print()
        
        # 顯示中優先級問題
        if medium_issues:
            print("🟡 中優先級問題：")
            for issue in medium_issues[:10]:  # 只顯示前10個
                print(f"  📁 {issue['file']}")
                print(f"  📍 第{issue['line']}行: {issue['content']}")
                print(f"  ⚠️  {issue['type']}")
                print()
            if len(medium_issues) > 10:
                print(f"  ... 還有 {len(medium_issues) - 10} 個中優先級問題")
                print()
        
        # 統計信息
        print("📊 問題類型統計：")
        issue_types = {}
        for issue in self.issues:
            issue_types[issue['type']] = issue_types.get(issue['type'], 0) + 1
        
        for issue_type, count in sorted(issue_types.items()):
            print(f"  {issue_type}: {count}")
    
    def run_all_checks(self):
        """運行所有檢查"""
        print("開始代碼質量檢查...")
        
        self.check_print_statements()
        self.check_exception_handling()
        self.check_imports()
        self.check_docstrings()
        
        self.generate_report()

def main():
    """主函數"""
    # 獲取項目根目錄
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    checker = CodeQualityChecker(project_root)
    checker.run_all_checks()

if __name__ == "__main__":
    main() 