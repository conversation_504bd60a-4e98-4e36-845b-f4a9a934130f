from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                             QLabel, QLineEdit, QComboBox, QFrame, QToolBar, QAction,
                             QDateEdit, QFormLayout, QGroupBox, QFileDialog)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager
from app.utils.logger import system_logger
from app.ui.invoice_dialog import InvoiceDialog

class InvoiceManager(QWidget):
    def __init__(self, db_manager, main_window=None, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.main_window = main_window  # 存儲主窗口引用
        self.permission_mgr = permission_mgr  # 儲存權限管理器引用
        
        self.init_ui()
        
        # 初始化時加載真實數據
        self.load_invoices()
        
        # 設置權限
        self.setup_permissions()
        
    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.btn_add, self.btn_edit, self.btn_delete, self.btn_to_delivery]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons)
        
    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('invoice'))
        main_layout = QVBoxLayout(self)
        
        # 添加頁面標題
        title_layout = QHBoxLayout()
        title_label = QLabel(self.lang_manager.get_text('invoice'))
        title_label.setObjectName("page-title")
        title_label.setFont(title_label.font())
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 搜索和過濾區域
        filter_group = QGroupBox(self.lang_manager.get_text('search_and_filter'))
        filter_layout = QFormLayout(filter_group)
        
        # 水平布局用於搜索
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        search_btn = QPushButton(self.lang_manager.get_text('search'))
        search_btn.setIcon(self.icon_manager.get_icon('search'))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        filter_layout.addRow(self.lang_manager.get_text('search_criteria'), search_layout)
        
        # 日期過濾器
        date_layout = QHBoxLayout()
        self.date_from = QDateEdit(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit(QDate.currentDate())
        self.date_from.setCalendarPopup(True)
        self.date_to.setCalendarPopup(True)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('from')))
        date_layout.addWidget(self.date_from)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('to')))
        date_layout.addWidget(self.date_to)
        filter_layout.addRow(self.lang_manager.get_text('date_range'), date_layout)
        
        # 付款狀態過濾器
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.lang_manager.get_text('all_status'))
        self.status_filter.addItem(self.lang_manager.get_text('unpaid'))
        self.status_filter.addItem(self.lang_manager.get_text('partially_paid'))
        self.status_filter.addItem(self.lang_manager.get_text('paid'))
        filter_layout.addRow(self.lang_manager.get_text('payment_status'), self.status_filter)
        
        main_layout.addWidget(filter_group)
        
        # 按鈕工具列 - 使用QHBoxLayout代替QToolBar，以實現更好的佈局控制
        buttons_layout = QHBoxLayout()
        
        # 創建分組容器以美化佈局
        basic_actions = QGroupBox("")
        basic_actions.setFlat(True)
        basic_layout = QHBoxLayout(basic_actions)
        basic_layout.setContentsMargins(0, 0, 0, 0)
        basic_layout.setSpacing(2)
        
        # 基本操作按鈕
        self.btn_add = QPushButton(self.lang_manager.get_text('add_invoice'))
        self.btn_add.setIcon(self.icon_manager.get_icon('add'))
        self.btn_add.setMinimumWidth(120)
        
        self.btn_edit = QPushButton(self.lang_manager.get_text('edit_invoice'))
        self.btn_edit.setIcon(self.icon_manager.get_icon('edit'))
        self.btn_edit.setMinimumWidth(120)
        
        self.btn_delete = QPushButton(self.lang_manager.get_text('delete_invoice'))
        self.btn_delete.setIcon(self.icon_manager.get_icon('delete'))
        self.btn_delete.setMinimumWidth(120)
        
        self.btn_view = QPushButton(self.lang_manager.get_text('view_invoice'))
        self.btn_view.setIcon(self.icon_manager.get_icon('view'))
        self.btn_view.setMinimumWidth(120)
        
        basic_layout.addWidget(self.btn_add)
        basic_layout.addWidget(self.btn_edit)
        basic_layout.addWidget(self.btn_delete)
        basic_layout.addWidget(self.btn_view)
        buttons_layout.addWidget(basic_actions)
        
        # 垂直分隔線
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line)
        
        # 轉換操作按鈕組
        convert_actions = QGroupBox("")
        convert_actions.setFlat(True)
        convert_layout = QHBoxLayout(convert_actions)
        convert_layout.setContentsMargins(0, 0, 0, 0)
        convert_layout.setSpacing(2)
        
        self.btn_to_delivery = QPushButton(self.lang_manager.get_text('to_delivery_note'))
        self.btn_to_delivery.setIcon(self.icon_manager.get_icon('delivery'))
        self.btn_to_delivery.setMinimumWidth(120)
        
        convert_layout.addWidget(self.btn_to_delivery)
        buttons_layout.addWidget(convert_actions)
        
        # 垂直分隔線
        separator_line2 = QFrame()
        separator_line2.setFrameShape(QFrame.VLine)
        separator_line2.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line2)
        
        # 輸出操作按鈕組
        output_actions = QGroupBox("")
        output_actions.setFlat(True)
        output_layout = QHBoxLayout(output_actions)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(2)
        
        self.btn_print = QPushButton(self.lang_manager.get_text('print'))
        self.btn_print.setIcon(self.icon_manager.get_icon('print'))
        self.btn_print.setMinimumWidth(80)
        
        self.btn_export = QPushButton(self.lang_manager.get_text('export'))
        self.btn_export.setIcon(self.icon_manager.get_icon('export'))
        self.btn_export.setMinimumWidth(80)
        
        output_layout.addWidget(self.btn_print)
        output_layout.addWidget(self.btn_export)
        buttons_layout.addWidget(output_actions)
        
        # 添加彈性空間，使按鈕靠左對齊
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # 添加一條分隔線
        bottom_separator = QFrame()
        bottom_separator.setFrameShape(QFrame.HLine)
        bottom_separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(bottom_separator)
        
        # 發票表格
        self.table = QTableWidget(0, 8)  # ID, 發票號, 客戶, 日期, 付款到期日, 總金額, 付款狀態, 轉換狀態
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('invoice_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('date'),
            self.lang_manager.get_text('payment_due'),
            self.lang_manager.get_text('total_amount'),
            self.lang_manager.get_text('payment_status'),
            '轉換狀態'
        ])
        
        # 隱藏ID列
        self.table.setColumnHidden(0, True)
        
        # 表格佈局
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(self.table.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        main_layout.addWidget(self.table)
        
        # 狀態標籤
        self.status_label = QLabel(f"{self.lang_manager.get_text('total_records')}: 0")
        main_layout.addWidget(self.status_label)
        
        # 連接按鈕事件
        self.btn_add.clicked.connect(self.add_invoice)
        self.btn_edit.clicked.connect(self.edit_invoice)
        self.btn_delete.clicked.connect(self.delete_invoice)
        self.btn_view.clicked.connect(self.view_invoice)
        self.btn_to_delivery.clicked.connect(self.convert_to_delivery)
        self.btn_print.clicked.connect(self.print_invoice)
        self.btn_export.clicked.connect(self.export_invoice)
        
        search_btn.clicked.connect(self.search_invoice)
        self.status_filter.currentIndexChanged.connect(self.filter_by_status)
        
    def add_invoice(self):
        """新增發票"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        dialog = InvoiceDialog(self, self.db_manager)
        if dialog.exec_():
            # 獲取數據
            invoice_data = dialog.get_data()
            
            # 保存到數據庫
            success = self.db_manager.add_invoice(invoice_data)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                # 刷新發票列表
                self.load_invoices()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def edit_invoice(self):
        """編輯發票"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取發票ID
        row = selected_rows[0].row()
        invoice_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取發票數據
        invoice_data = self.db_manager.get_invoice(invoice_id)
        
        if not invoice_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('invoice_not_found')
            )
            return
        
        # 打開編輯對話框
        dialog = InvoiceDialog(self, self.db_manager, invoice_data)
        if dialog.exec_():
            # 獲取更新後的數據
            updated_data = dialog.get_data()
            
            # 更新數據庫
            success = self.db_manager.update_invoice(invoice_id, updated_data)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
                # 刷新發票列表
                self.load_invoices()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def delete_invoice(self):
        """刪除發票"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取發票ID和編號
        row = selected_rows[0].row()
        invoice_id = int(self.table.item(row, 0).text())
        invoice_no = self.table.item(row, 1).text()
        
        # 確認刪除
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('delete_invoice_confirm').format(invoice_no=invoice_no),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 獲取當前用戶信息
            deleted_by = "system"
            if self.main_window and hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                deleted_by = self.main_window.current_user.get('username', 'system')
            
            # 從數據庫刪除（移動到回收站）
            success = self.db_manager.delete_invoice(invoice_id, deleted_by)
            
            if success:
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    "發票已移動到回收站"
                )
                # 刷新發票列表
                self.load_invoices()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def view_invoice(self):
        """查看發票"""
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取發票ID
        row = selected_rows[0].row()
        invoice_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取發票數據
        invoice_data = self.db_manager.get_invoice(invoice_id)
        
        if not invoice_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('invoice_not_found')
            )
            return
        
        # 打開發票對話框（只讀模式）
        dialog = InvoiceDialog(self, self.db_manager, invoice_data)
        # 禁用所有輸入控件
        dialog.setReadOnly(True)
        dialog.btn_save.setVisible(False)
        dialog.btn_preview.setText(self.lang_manager.get_text('print'))
        dialog.btn_cancel.setText(self.lang_manager.get_text('close'))
        dialog.exec_()
    
    def convert_to_delivery(self):
        """將發票轉換為送貨單"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取發票ID
        row = selected_rows[0].row()
        invoice_id = int(self.table.item(row, 0).text())
        invoice_no = self.table.item(row, 1).text()
        
        # 檢查發票是否已轉換為送貨單
        if self.db_manager.check_invoice_converted_to_delivery(invoice_id):
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                f"此發票 ({invoice_no}) 已被轉換為送貨單，不能重複轉換。"
            )
            return
        
        # 確認轉換
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('invoice_to_delivery_confirm').format(invoice_no=invoice_no),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 從數據庫獲取發票數據
            invoice_data = self.db_manager.get_invoice(invoice_id)
            
            if not invoice_data:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('invoice_not_found')
                )
                return
            
            # 轉換為送貨單數據
            delivery_data = self.db_manager.convert_invoice_to_delivery(invoice_id)
            
            if delivery_data:
                # 送貨單創建成功
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('delivery_note_created')
                )
                
                # 選擇最可靠的方式獲取主窗口
                if self.main_window and hasattr(self.main_window, 'show_delivery_note_manager'):
                    # 方法1: 使用傳入的主窗口引用 (最可靠)
                    self.main_window.show_delivery_note_manager()
                    return
                
                # 方法2: 使用全局變量
                try:
                    from app.main import global_main_window
                    if global_main_window and hasattr(global_main_window, 'show_delivery_note_manager'):
                        global_main_window.show_delivery_note_manager()
                        return
                except (ImportError, AttributeError):
                    # 全局變量不可用，繼續嘗試其他方法
                    system_logger.debug("無法通過全局變量獲取主窗口引用")
                
                # 方法3: 找到父窗口
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if widget.__class__.__name__ == 'MainWindow' and hasattr(widget, 'show_delivery_note_manager'):
                        widget.show_delivery_note_manager()
                        return
                
                # 如果所有方法都失敗，顯示錯誤並刷新當前發票列表
                QMessageBox.warning(
                    self,
                    self.lang_manager.get_text('error'),
                    "無法切換到送貨單管理頁面，但送貨單已成功創建。請手動切換到送貨單頁面查看。"
                )
                self.load_invoices()
            else:
                QMessageBox.warning(
                    self, 
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def print_invoice(self):
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def export_invoice(self):
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def search_invoice(self):
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def filter_by_status(self):
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()
        self.setWindowTitle(self.lang_manager.get_text('invoice'))
        
        # 更新搜索區域
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        
        # 更新按鈕文字
        self.btn_add.setText(self.lang_manager.get_text('add_invoice'))
        self.btn_edit.setText(self.lang_manager.get_text('edit_invoice'))
        self.btn_delete.setText(self.lang_manager.get_text('delete_invoice'))
        self.btn_view.setText(self.lang_manager.get_text('view_invoice'))
        self.btn_to_delivery.setText(self.lang_manager.get_text('to_delivery_note'))
        self.btn_print.setText(self.lang_manager.get_text('print'))
        self.btn_export.setText(self.lang_manager.get_text('export'))
        
        # 更新表格標題
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('invoice_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('date'),
            self.lang_manager.get_text('payment_due'),
            self.lang_manager.get_text('total_amount'),
            self.lang_manager.get_text('payment_status'),
            '轉換狀態'
        ])
        
        # 更新狀態標籤
        self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}")
    
    def load_invoices(self, search_text="", date_from=None, date_to=None, status=None):
        """從數據庫加載發票數據"""
        if self.db_manager:
            # 獲取發票列表
            invoices = self.db_manager.get_invoices(search_text, date_from, date_to, status)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            for invoice in invoices:
                row = self.table.rowCount()
                self.table.insertRow(row)
                
                # ID列
                self.table.setItem(row, 0, QTableWidgetItem(str(invoice.get('id', ''))))
                
                # 發票號
                self.table.setItem(row, 1, QTableWidgetItem(invoice.get('invoice_no', '')))
                
                # 客戶
                self.table.setItem(row, 2, QTableWidgetItem(invoice.get('customer_name', '')))
                
                # 日期
                self.table.setItem(row, 3, QTableWidgetItem(invoice.get('date', '')))
                
                # 付款到期日
                self.table.setItem(row, 4, QTableWidgetItem(invoice.get('payment_due', '')))
                
                # 總金額
                total_amount = invoice.get('total_amount', 0)
                total_amount_text = f"{total_amount:,.2f}" if isinstance(total_amount, (int, float)) else total_amount
                self.table.setItem(row, 5, QTableWidgetItem(total_amount_text))
                
                # 付款狀態
                self.table.setItem(row, 6, QTableWidgetItem(invoice.get('payment_status', '')))
                
                # 轉換狀態 - 檢查是否已轉換為送貨單
                invoice_id = invoice.get('id')
                if invoice_id and self.db_manager.check_invoice_converted_to_delivery(invoice_id):
                    conversion_status = "已轉換"
                else:
                    conversion_status = "未轉換"
                self.table.setItem(row, 7, QTableWidgetItem(conversion_status))
            
            # 更新狀態標籤
            self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}")
        # 刪除對測試數據的調用
        # else:
        #     # 在測試環境中，加載測試數據
        #     self.add_test_data() 