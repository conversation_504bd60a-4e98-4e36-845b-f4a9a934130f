from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLabel, QLineEdit, 
                           QFormLayout, QDialog, QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt
from app.utils.language_manager import LanguageManager
from app.ui.customer_dialog import CustomerDialog
from app.database.db_manager import DatabaseManager
from app.utils.icon_manager import IconManager
from app.utils.logger import system_logger

class CustomerManager(QWidget):
    """客戶管理界面"""
    
    def __init__(self, db_manager, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.permission_mgr = permission_mgr
        self.icon_manager = IconManager()
        
        # 初始化界面
        self.init_ui()
        
        # 載入客戶數據
        self.load_customers()
        
        # 設置權限
        self.setup_permissions()
        
    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.add_btn, self.edit_btn, self.delete_btn]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons)
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(self.lang_manager.get_text('customer_management'))
        layout = QVBoxLayout()
        
        # 頂部控制區域
        top_layout = QHBoxLayout()
        
        # 搜索區域
        search_label = QLabel(self.lang_manager.get_text('search'))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        self.search_input.textChanged.connect(self.filter_customers)
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_input)
        top_layout.addStretch()
        
        # 按鈕區域
        self.add_btn = QPushButton(self.lang_manager.get_text('add_customer'))
        self.add_btn.setIcon(self.icon_manager.get_icon('add'))
        self.add_btn.clicked.connect(self.add_customer)
        
        self.edit_btn = QPushButton(self.lang_manager.get_text('edit_customer'))
        self.edit_btn.setIcon(self.icon_manager.get_icon('edit'))
        self.edit_btn.clicked.connect(self.edit_customer)
        
        self.delete_btn = QPushButton(self.lang_manager.get_text('delete_customer'))
        self.delete_btn.setIcon(self.icon_manager.get_icon('delete'))
        self.delete_btn.clicked.connect(self.delete_customer)
        
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.delete_btn)
        
        layout.addLayout(top_layout)
        
        # 客戶表格
        self.customer_table = QTableWidget(0, 6)  # 6列：ID, 名稱, 聯絡人, 電話, 電子郵件, 地址
        self.customer_table.setHorizontalHeaderLabels([
            'ID', 
            self.lang_manager.get_text('customer_name'),
            self.lang_manager.get_text('contact_person'),
            self.lang_manager.get_text('phone'),
            self.lang_manager.get_text('email'),
            self.lang_manager.get_text('address')
        ])
        
        # 設置表格屬性
        self.customer_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.customer_table.setSelectionMode(QTableWidget.SingleSelection)
        self.customer_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.customer_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.customer_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.customer_table)
        
    def load_customers(self):
        """從數據庫加載客戶數據"""
        try:
            # 清空表格
            self.customer_table.setRowCount(0)
            
            # 獲取所有客戶資料
            customers = self.db_manager.get_customers()
            
            # 填充表格
            for i, customer in enumerate(customers):
                self.customer_table.insertRow(i)
                # 按順序填充表格列
                columns = ['id', 'name', 'contact_person', 'phone', 'email', 'address']
                for j, column in enumerate(columns):
                    value = customer.get(column, '')
                    if value is None:
                        value = ''
                    self.customer_table.setItem(i, j, QTableWidgetItem(str(value)))
                    
        except Exception as e:
            system_logger.error(f"加載客戶數據時出錯: {str(e)}")
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('operation_failed')}\n{str(e)}"
            )
            
    def filter_customers(self):
        """根據搜索條件過濾客戶列表"""
        search_text = self.search_input.text().lower()
        
        for i in range(self.customer_table.rowCount()):
            show_row = False
            for j in range(1, 6):  # 搜索名稱、聯絡人、電話、電子郵件、地址列
                item = self.customer_table.item(i, j)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.customer_table.setRowHidden(i, not show_row)
            
    def add_customer(self):
        """添加新客戶"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        dialog = CustomerDialog(self, self.lang_manager, self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            # 重新加載客戶數據
            self.load_customers()
            
    def edit_customer(self):
        """編輯選中的客戶"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取選中的行
        selected_items = self.customer_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取客戶ID
        row = selected_items[0].row()
        customer_id = int(self.customer_table.item(row, 0).text())
        
        # 打開編輯對話框
        dialog = CustomerDialog(self, self.lang_manager, self.db_manager, customer_id)
        if dialog.exec_() == QDialog.Accepted:
            # 重新加載客戶數據
            self.load_customers()
            
    def delete_customer(self):
        """刪除選中的客戶"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取選中的行
        selected_items = self.customer_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取客戶ID和名稱
        row = selected_items[0].row()
        customer_id = int(self.customer_table.item(row, 0).text())
        customer_name = self.customer_table.item(row, 1).text()
        
        # 確認刪除
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('delete_confirm'),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 刪除客戶
            if self.db_manager.delete_customer(customer_id):
                # 刪除成功，重新加載客戶數據
                self.load_customers()
                QMessageBox.information(
                    self,
                    self.lang_manager.get_text('success'),
                    self.lang_manager.get_text('operation_success')
                )
            else:
                # 刪除失敗
                QMessageBox.critical(
                    self,
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
                
    def refresh_translations(self):
        """更新界面翻譯"""
        # 重新加載語言管理器
        self.lang_manager = LanguageManager()
        
        # 更新按鈕文字
        self.add_btn.setText(self.lang_manager.get_text('add_customer'))
        self.edit_btn.setText(self.lang_manager.get_text('edit_customer'))
        self.delete_btn.setText(self.lang_manager.get_text('delete_customer'))
        
        # 更新表格頭
        self.customer_table.setHorizontalHeaderLabels([
            'ID', 
            self.lang_manager.get_text('customer_name'),
            self.lang_manager.get_text('contact_person'),
            self.lang_manager.get_text('phone'),
            self.lang_manager.get_text('email'),
            self.lang_manager.get_text('address')
        ]) 