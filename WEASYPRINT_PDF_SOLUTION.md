# WeasyPrint PDF生成解決方案

## 概述

本項目已升級為使用**WeasyPrint**作為主要的PDF生成引擎，提供更專業、更精確的PDF輸出控制。WeasyPrint是一個現代化的HTML/CSS到PDF轉換工具，能夠生成符合商業標準的專業文檔。

## 主要改進

### 1. 技術升級
- **從HTML手動打印** → **WeasyPrint自動生成PDF**
- **從ReportLab複雜編程** → **HTML/CSS簡潔設計**
- **從多個生成器** → **統一的專業解決方案**

### 2. 輸出質量提升
- ✅ **原生PDF格式**：直接生成PDF，無需瀏覽器轉換
- ✅ **精確的佈局控制**：使用CSS精確控制每個元素
- ✅ **專業字體支持**：完美支持Times New Roman等商業字體
- ✅ **高質量渲染**：矢量圖形和清晰文字
- ✅ **多頁面支持**：自動分頁和頁面管理

### 3. 開發效率提升
- ✅ **HTML/CSS設計**：使用熟悉的Web技術
- ✅ **即時預覽**：可在瀏覽器中預覽HTML
- ✅ **易於維護**：清晰的代碼結構
- ✅ **靈活定制**：CSS樣式易於調整

## 文件結構

```
app/utils/
├── pdf_generator_weasy.py     # WeasyPrint PDF生成器（新）
├── pdf_manager.py             # 統一PDF管理器（更新）
└── logger.py                  # 日誌系統

根目錄/
├── test_weasyprint_generator.py    # WeasyPrint測試腳本
├── install_weasyprint.py           # 自動安裝腳本
├── requirements.txt                # 更新的依賴列表
└── output/                         # PDF輸出目錄
```

## 安裝說明

### 自動安裝（推薦）
```bash
python install_weasyprint.py
```

### 手動安裝

#### Windows
```bash
pip install weasyprint
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install python3-dev python3-pip python3-cffi python3-brotli
sudo apt-get install libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0
sudo apt-get install libffi-dev shared-mime-info
pip install weasyprint
```

#### macOS
```bash
brew install pango harfbuzz libffi
pip install weasyprint
```

## 使用方法

### 1. 基本使用
```python
from app.utils.pdf_generator_weasy import PDFGeneratorWeasy

# 創建生成器
pdf_generator = PDFGeneratorWeasy()

# 準備數據
quotation_data = {
    'quotation_no': 'Q2025-001',
    'date': 'MAY 28, 25',
    'customer_name': 'CUSTOMER COMPANY LIMITED',
    'items': [
        {
            'model': 'PRODUCT-001',
            'description': 'Product description',
            'quantity': 1.0,
            'unit_price': 1000.00,
            'amount': 1000.00
        }
    ],
    'total_amount': 1000.00
}

# 生成PDF
pdf_path = pdf_generator.generate_quotation_pdf(quotation_data)
print(f"PDF已生成: {pdf_path}")
```

### 2. 通過PDF管理器使用
```python
from app.utils.pdf_manager import PDFManager

# 創建管理器（自動選擇最佳生成器）
pdf_manager = PDFManager()

# 生成報價單
quotation_pdf = pdf_manager.generate_quotation_pdf(quotation_data)

# 生成發票
invoice_pdf = pdf_manager.generate_invoice_pdf(invoice_data)

# 生成送貨單
delivery_pdf = pdf_manager.generate_delivery_note_pdf(delivery_data)
```

## 功能特點

### 1. 文檔類型支持
- **報價單 (Quotation)**：完整的商業報價文檔
- **發票 (Invoice)**：專業發票格式
- **送貨單 (Delivery Note)**：送貨確認文檔

### 2. 佈局特點
- **專業頁首**：公司信息和客戶信息並排顯示
- **文檔信息表格**：日期、編號、銷售員等信息
- **項目列表**：詳細的產品信息和價格表
- **總計區域**：清晰的金額總計
- **條款和簽名**：完整的商業條款和簽名區域
- **專業頁尾**：頁碼和公司信息

### 3. 多頁面支持
- **自動分頁**：根據內容自動分頁
- **頁面計算**：智能計算所需頁數
- **一致性佈局**：所有頁面保持一致的格式
- **頁尾信息**：每頁顯示頁碼和基本信息

### 4. 樣式特點
- **Times New Roman字體**：專業商業文檔標準
- **精確間距**：使用pt單位精確控制
- **專業表格**：清晰的邊框和對齊
- **層次分明**：不同字體大小區分重要性

## 測試和驗證

### 1. 運行測試
```bash
python test_weasyprint_generator.py
```

### 2. 測試內容
- ✅ WeasyPrint安裝驗證
- ✅ 基本PDF生成測試
- ✅ 多文檔類型測試
- ✅ 多頁面文檔測試
- ✅ PDF管理器集成測試

### 3. 輸出驗證
- 檢查`output/`目錄中的PDF文件
- 驗證文檔格式和佈局
- 確認多頁面文檔的一致性

## 優勢對比

| 特點 | WeasyPrint | HTML手動打印 | ReportLab |
|------|------------|--------------|-----------|
| 輸出質量 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 開發效率 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 維護性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 自動化 | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 跨平台 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 依賴複雜度 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 故障排除

### 1. 安裝問題
```bash
# 如果WeasyPrint安裝失敗
pip install --upgrade pip
pip install --no-cache-dir weasyprint

# Linux系統缺少依賴
sudo apt-get install build-essential python3-dev
```

### 2. 字體問題
```python
# 如果字體顯示異常，檢查系統字體
from weasyprint.text.fonts import FontConfiguration
font_config = FontConfiguration()
```

### 3. 內存問題
```python
# 對於大型文檔，可以調整內存設置
import gc
gc.collect()  # 在生成大量PDF後清理內存
```

## 未來擴展

### 1. 可能的增強功能
- **自定義字體**：支持更多字體選擇
- **圖片支持**：在PDF中嵌入圖片和logo
- **數字簽名**：添加數字簽名功能
- **批量生成**：批量處理多個文檔
- **模板系統**：可配置的文檔模板

### 2. 性能優化
- **緩存機制**：緩存常用的CSS和模板
- **並行處理**：支持多線程PDF生成
- **增量更新**：只重新生成變更的部分

## 總結

WeasyPrint PDF生成解決方案為項目提供了：

1. **專業品質**：符合商業標準的PDF輸出
2. **開發效率**：使用HTML/CSS的熟悉技術
3. **維護便利**：清晰的代碼結構和文檔
4. **擴展性**：易於添加新功能和樣式
5. **穩定性**：成熟的開源解決方案

這個解決方案完全替代了之前的HTML手動打印和複雜的ReportLab編程，提供了更好的用戶體驗和開發體驗。 