from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont
from PyQt5.QtCore import Qt
from app.utils.logger import system_logger
import os

def create_placeholder_icons():
    """創建佔位圖標"""
    
    # 確保圖標目錄存在
    icon_dir = "app/assets/icons"
    os.makedirs(icon_dir, exist_ok=True)
    
    # 圖標名稱列表
    icon_names = [
        "quotation", "invoice", "delivery", "customer", "product", "settings",
        "user", "add", "edit", "delete", "view", "save", "cancel", "search",
        "language", "export", "print", "company", "logout", "backup", "restore",
        "database", "login", "menu", "system", "document", "data"
    ]
    
    # 為每個圖標定義顏色
    colors = {
        "quotation": (52, 152, 219),    # Blue
        "invoice": (46, 204, 113),      # Green
        "delivery": (155, 89, 182),     # Purple
        "customer": (241, 196, 15),     # Yellow
        "product": (230, 126, 34),      # Orange
        "settings": (149, 165, 166),    # <PERSON>
        "user": (52, 73, 94),           # Dark Blue
        "add": (39, 174, 96),           # Green
        "edit": (243, 156, 18),         # Orange
        "delete": (231, 76, 60),        # Red
        "view": (52, 152, 219),         # Blue
        "save": (39, 174, 96),          # Green
        "cancel": (231, 76, 60),        # Red
        "search": (155, 89, 182),       # Purple
        "language": (52, 152, 219),     # Blue
        "export": (46, 204, 113),       # Green
        "print": (127, 140, 141),       # Gray
        "company": (52, 73, 94),        # Dark Blue
        "logout": (231, 76, 60),        # Red
        "backup": (39, 174, 96),        # Green
        "restore": (243, 156, 18),      # Orange
        "database": (155, 89, 182),     # Purple
        "login": (52, 152, 219),        # Blue
        "menu": (149, 165, 166),        # Gray
        "system": (52, 73, 94),         # Dark Blue
        "document": (46, 204, 113),     # Green
        "data": (243, 156, 18)          # Orange
    }
    
    # 創建每個圖標
    for icon_name in icon_names:
        icon_path = os.path.join(icon_dir, f"{icon_name}.png")
        
        # 如果圖標已存在，跳過
        if os.path.exists(icon_path):
            continue
            
        # 創建 32x32 的圖標
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 設置顏色
        color = colors.get(icon_name, (127, 140, 141))  # 默認灰色
        painter.setBrush(QColor(*color))
        painter.setPen(QColor(*color))
        
        # 繪製圓形背景
        painter.drawEllipse(2, 2, 28, 28)
        
        # 添加文字（圖標名稱的首字母）
        painter.setPen(QColor(255, 255, 255))  # 白色文字
        font = QFont("Arial", 12, QFont.Bold)
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, icon_name[0].upper())
        
        painter.end()
        
        # 保存圖標
        pixmap.save(icon_path)
    
    system_logger.info(f"已創建 {len(icon_names)} 個圖標佔位符")

if __name__ == "__main__":
    # 如果直接運行此腳本，將創建所有圖標
    app = QApplication([])
    create_placeholder_icons() 