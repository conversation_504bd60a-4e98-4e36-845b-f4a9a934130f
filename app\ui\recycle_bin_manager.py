from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                             QLabel, QLineEdit, QComboBox, QFrame, QGroupBox, QFormLayout,
                             QDateEdit, QTextEdit, QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon, QFont
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager
from app.utils.logger import system_logger

class RecycleBinManager(QWidget):
    def __init__(self, db_manager, main_window=None, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.main_window = main_window
        self.permission_mgr = permission_mgr
        
        self.init_ui()
        self.load_recycle_bin_items()
        self.setup_permissions()
        
    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.btn_restore, self.btn_permanent_delete, self.btn_clear_all]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons)
        
    def init_ui(self):
        self.setWindowTitle("回收站管理")
        main_layout = QVBoxLayout(self)
        
        # 添加頁面標題
        title_layout = QHBoxLayout()
        title_label = QLabel("回收站管理")
        title_label.setObjectName("page-title")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 過濾區域
        filter_group = QGroupBox("篩選條件")
        filter_layout = QFormLayout(filter_group)
        
        # 項目類型過濾器
        self.type_filter = QComboBox()
        self.type_filter.addItem("所有類型")
        self.type_filter.addItem("報價單")
        self.type_filter.addItem("發票")
        self.type_filter.addItem("送貨單")
        self.type_filter.addItem("客戶")
        self.type_filter.addItem("產品")
        filter_layout.addRow("項目類型:", self.type_filter)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索項目標題...")
        search_btn = QPushButton("搜索")
        search_btn.setIcon(self.icon_manager.get_icon('search'))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        filter_layout.addRow("搜索:", search_layout)
        
        main_layout.addWidget(filter_group)
        
        # 操作按鈕區域
        buttons_layout = QHBoxLayout()
        
        # 基本操作按鈕組
        basic_actions = QGroupBox("")
        basic_actions.setFlat(True)
        basic_layout = QHBoxLayout(basic_actions)
        basic_layout.setContentsMargins(0, 0, 0, 0)
        basic_layout.setSpacing(5)
        
        self.btn_restore = QPushButton("還原項目")
        self.btn_restore.setIcon(self.icon_manager.get_icon('restore'))
        self.btn_restore.setMinimumWidth(100)
        
        self.btn_permanent_delete = QPushButton("永久刪除")
        self.btn_permanent_delete.setIcon(self.icon_manager.get_icon('delete'))
        self.btn_permanent_delete.setMinimumWidth(100)
        self.btn_permanent_delete.setStyleSheet("QPushButton { color: red; }")
        
        self.btn_view_details = QPushButton("查看詳情")
        self.btn_view_details.setIcon(self.icon_manager.get_icon('view'))
        self.btn_view_details.setMinimumWidth(100)
        
        basic_layout.addWidget(self.btn_restore)
        basic_layout.addWidget(self.btn_permanent_delete)
        basic_layout.addWidget(self.btn_view_details)
        buttons_layout.addWidget(basic_actions)
        
        # 垂直分隔線
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line)
        
        # 批量操作按鈕組
        batch_actions = QGroupBox("")
        batch_actions.setFlat(True)
        batch_layout = QHBoxLayout(batch_actions)
        batch_layout.setContentsMargins(0, 0, 0, 0)
        batch_layout.setSpacing(5)
        
        self.btn_clear_all = QPushButton("清空回收站")
        self.btn_clear_all.setIcon(self.icon_manager.get_icon('clear'))
        self.btn_clear_all.setMinimumWidth(100)
        self.btn_clear_all.setStyleSheet("QPushButton { color: red; }")
        
        self.btn_clear_old = QPushButton("清理30天前")
        self.btn_clear_old.setIcon(self.icon_manager.get_icon('cleanup'))
        self.btn_clear_old.setMinimumWidth(100)
        
        self.btn_refresh = QPushButton("刷新")
        self.btn_refresh.setIcon(self.icon_manager.get_icon('refresh'))
        self.btn_refresh.setMinimumWidth(80)
        
        batch_layout.addWidget(self.btn_clear_all)
        batch_layout.addWidget(self.btn_clear_old)
        batch_layout.addWidget(self.btn_refresh)
        buttons_layout.addWidget(batch_actions)
        
        # 添加彈性空間
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)
        
        # 添加分隔線
        bottom_separator = QFrame()
        bottom_separator.setFrameShape(QFrame.HLine)
        bottom_separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(bottom_separator)
        
        # 創建分割器用於上下布局
        splitter = QSplitter(Qt.Vertical)
        
        # 回收站項目表格
        self.table = QTableWidget(0, 6)  # ID, 類型, 標題, 刪除者, 刪除時間, 原始表
        self.table.setHorizontalHeaderLabels([
            'ID',
            '項目類型',
            '項目標題',
            '刪除者',
            '刪除時間',
            '原始表'
        ])
        
        # 隱藏ID列
        self.table.setColumnHidden(0, True)
        
        # 表格佈局
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(self.table.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        splitter.addWidget(self.table)
        
        # 詳情顯示區域
        details_group = QGroupBox("項目詳情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        self.details_text.setPlainText("請選擇一個項目查看詳情...")
        
        details_layout.addWidget(self.details_text)
        splitter.addWidget(details_group)
        
        # 設置分割器比例
        splitter.setSizes([400, 200])
        main_layout.addWidget(splitter)
        
        # 狀態標籤
        self.status_label = QLabel("總計項目: 0")
        main_layout.addWidget(self.status_label)
        
        # 連接信號
        self.btn_restore.clicked.connect(self.restore_item)
        self.btn_permanent_delete.clicked.connect(self.permanent_delete_item)
        self.btn_view_details.clicked.connect(self.view_item_details)
        self.btn_clear_all.clicked.connect(self.clear_all_items)
        self.btn_clear_old.clicked.connect(self.clear_old_items)
        self.btn_refresh.clicked.connect(self.load_recycle_bin_items)
        
        search_btn.clicked.connect(self.search_items)
        self.type_filter.currentIndexChanged.connect(self.filter_by_type)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
    def load_recycle_bin_items(self):
        """加載回收站項目"""
        try:
            # 獲取過濾條件
            type_filter = self.type_filter.currentText()
            if type_filter == "所有類型":
                type_filter = None
            else:
                # 轉換中文類型名稱為英文
                type_mapping = {
                    "報價單": "quotation",
                    "發票": "invoice", 
                    "送貨單": "delivery_note",
                    "客戶": "customer",
                    "產品": "product"
                }
                type_filter = type_mapping.get(type_filter)
            
            # 從數據庫獲取回收站項目
            items = self.db_manager.get_recycle_bin_items(type_filter)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            for item in items:
                row = self.table.rowCount()
                self.table.insertRow(row)
                
                # ID列
                self.table.setItem(row, 0, QTableWidgetItem(str(item['id'])))
                
                # 項目類型（轉換為中文）
                type_mapping = {
                    "quotation": "報價單",
                    "invoice": "發票",
                    "delivery_note": "送貨單",
                    "customer": "客戶",
                    "product": "產品"
                }
                item_type_cn = type_mapping.get(item['item_type'], item['item_type'])
                self.table.setItem(row, 1, QTableWidgetItem(item_type_cn))
                
                # 項目標題
                self.table.setItem(row, 2, QTableWidgetItem(item['item_title']))
                
                # 刪除者
                self.table.setItem(row, 3, QTableWidgetItem(item['deleted_by'] or '系統'))
                
                # 刪除時間
                deleted_at = item['deleted_at']
                if deleted_at:
                    # 格式化時間顯示
                    from datetime import datetime
                    try:
                        dt = datetime.fromisoformat(deleted_at.replace('Z', '+00:00'))
                        formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, AttributeError):
                        formatted_time = deleted_at
                else:
                    formatted_time = ''
                self.table.setItem(row, 4, QTableWidgetItem(formatted_time))
                
                # 原始表
                self.table.setItem(row, 5, QTableWidgetItem(item['original_table']))
            
            # 更新狀態標籤
            self.status_label.setText(f"總計項目: {self.table.rowCount()}")
            
        except Exception as e:
            system_logger.error(f"加載回收站項目錯誤: {str(e)}")
            QMessageBox.warning(self, "錯誤", f"加載回收站項目失敗: {str(e)}")
    
    def on_selection_changed(self):
        """當選擇項目改變時更新詳情顯示"""
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            self.details_text.setPlainText("請選擇一個項目查看詳情...")
            return
        
        try:
            # 獲取選中項目的ID
            row = selected_rows[0].row()
            recycle_id = int(self.table.item(row, 0).text())
            
            # 從數據庫獲取詳細信息
            self.db_manager.connect()
            self.db_manager.cursor.execute("""
                SELECT item_data FROM recycle_bin WHERE id = ?
            """, (recycle_id,))
            
            result = self.db_manager.cursor.fetchone()
            self.db_manager.disconnect()
            
            if result:
                import json
                item_data = json.loads(result[0])
                
                # 格式化顯示詳情
                details = self.format_item_details(item_data)
                self.details_text.setPlainText(details)
            else:
                self.details_text.setPlainText("無法獲取項目詳情")
                
        except Exception as e:
            system_logger.error(f"獲取項目詳情錯誤: {str(e)}")
            self.details_text.setPlainText(f"獲取詳情失敗: {str(e)}")
    
    def format_item_details(self, item_data):
        """格式化項目詳情顯示"""
        details = []
        
        # 基本信息
        if 'quotation_no' in item_data:
            details.append(f"報價單號: {item_data['quotation_no']}")
        elif 'invoice_no' in item_data:
            details.append(f"發票號: {item_data['invoice_no']}")
        elif 'delivery_no' in item_data:
            details.append(f"送貨單號: {item_data['delivery_no']}")
        elif 'name' in item_data:
            details.append(f"名稱: {item_data['name']}")
        
        # 客戶信息
        if 'customer_name' in item_data:
            details.append(f"客戶: {item_data['customer_name']}")
        
        # 日期信息
        if 'date' in item_data:
            details.append(f"日期: {item_data['date']}")
        
        # 金額信息
        if 'total_amount' in item_data:
            details.append(f"總金額: {item_data['total_amount']}")
        
        # 狀態信息
        if 'status' in item_data:
            details.append(f"狀態: {item_data['status']}")
        elif 'payment_status' in item_data:
            details.append(f"付款狀態: {item_data['payment_status']}")
        
        # 項目列表
        if 'items' in item_data and item_data['items']:
            details.append(f"\n項目數量: {len(item_data['items'])}")
            details.append("項目明細:")
            for i, item in enumerate(item_data['items'][:5], 1):  # 只顯示前5個項目
                desc = item.get('description', '無描述')
                qty = item.get('quantity', 0)
                details.append(f"  {i}. {desc} (數量: {qty})")
            
            if len(item_data['items']) > 5:
                details.append(f"  ... 還有 {len(item_data['items']) - 5} 個項目")
        
        return '\n'.join(details)
    
    def restore_item(self):
        """還原選中的項目"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
        
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "請選擇要還原的項目")
            return
        
        row = selected_rows[0].row()
        recycle_id = int(self.table.item(row, 0).text())
        item_title = self.table.item(row, 2).text()
        
        # 確認還原
        reply = QMessageBox.question(
            self,
            "確認還原",
            f"確認要還原項目 '{item_title}' 嗎？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.db_manager.restore_from_recycle_bin(recycle_id)
            
            if success:
                QMessageBox.information(self, "成功", "項目已成功還原")
                self.load_recycle_bin_items()
            else:
                QMessageBox.warning(self, "錯誤", f"還原失敗: {message}")
    
    def permanent_delete_item(self):
        """永久刪除選中的項目"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
        
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "請選擇要永久刪除的項目")
            return
        
        row = selected_rows[0].row()
        recycle_id = int(self.table.item(row, 0).text())
        item_title = self.table.item(row, 2).text()
        
        # 確認永久刪除
        reply = QMessageBox.question(
            self,
            "確認永久刪除",
            f"確認要永久刪除項目 '{item_title}' 嗎？\n\n⚠️ 此操作不可撤銷！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.db_manager.permanently_delete_from_recycle_bin(recycle_id):
                QMessageBox.information(self, "成功", "項目已永久刪除")
                self.load_recycle_bin_items()
            else:
                QMessageBox.warning(self, "錯誤", "永久刪除失敗")
    
    def view_item_details(self):
        """查看項目詳情（在新窗口中）"""
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "請選擇要查看的項目")
            return
        
        # 詳情已在下方面板顯示，這裡可以實現更詳細的查看窗口
        QMessageBox.information(self, "提示", "項目詳情已在下方面板顯示")
    
    def clear_all_items(self):
        """清空所有回收站項目"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
        
        if self.table.rowCount() == 0:
            QMessageBox.information(self, "提示", "回收站已經是空的")
            return
        
        # 確認清空
        reply = QMessageBox.question(
            self,
            "確認清空回收站",
            f"確認要清空回收站中的所有 {self.table.rowCount()} 個項目嗎？\n\n⚠️ 此操作不可撤銷！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, count = self.db_manager.clear_recycle_bin()
            
            if success:
                QMessageBox.information(self, "成功", f"已清空回收站，刪除了 {count} 個項目")
                self.load_recycle_bin_items()
            else:
                QMessageBox.warning(self, "錯誤", "清空回收站失敗")
    
    def clear_old_items(self):
        """清理30天前的項目"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
        
        # 確認清理
        reply = QMessageBox.question(
            self,
            "確認清理過期項目",
            "確認要清理30天前的回收站項目嗎？\n\n⚠️ 此操作不可撤銷！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, count = self.db_manager.clear_recycle_bin(older_than_days=30)
            
            if success:
                if count > 0:
                    QMessageBox.information(self, "成功", f"已清理 {count} 個過期項目")
                else:
                    QMessageBox.information(self, "提示", "沒有找到30天前的項目")
                self.load_recycle_bin_items()
            else:
                QMessageBox.warning(self, "錯誤", "清理過期項目失敗")
    
    def search_items(self):
        """搜索項目"""
        search_text = self.search_input.text().strip()
        
        if not search_text:
            self.load_recycle_bin_items()
            return
        
        # 簡單的客戶端搜索
        for row in range(self.table.rowCount()):
            item_title = self.table.item(row, 2).text()
            if search_text.lower() in item_title.lower():
                self.table.setRowHidden(row, False)
            else:
                self.table.setRowHidden(row, True)
    
    def filter_by_type(self):
        """按類型過濾"""
        self.load_recycle_bin_items()
    
    def refresh_translations(self):
        """刷新界面翻譯"""
        # 這裡可以添加多語言支持
        pass 