import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from app.utils.placeholder_icons import create_placeholder_icons

# 全局變量，用於存儲主窗口實例
global_main_window = None

def setup_environment():
    """設置運行環境"""
    # 確保資源目錄存在
    os.makedirs("app/assets/icons", exist_ok=True)
    os.makedirs("app/assets/images", exist_ok=True)
    os.makedirs("database_file", exist_ok=True)
    
    # 創建佔位圖標
    create_placeholder_icons()

def show_error_and_exit(error_message, exception=None):
    """顯示錯誤訊息並退出應用"""
    app = QApplication.instance() or QApplication(sys.argv)
    
    error_text = f"{error_message}\n\n"
    if exception:
        error_text += f"異常詳情: {str(exception)}\n\n"
        error_text += "".join(traceback.format_tb(exception.__traceback__))
    
    QMessageBox.critical(None, "程序啟動錯誤", error_text)
    sys.exit(1)

if __name__ == "__main__":
    try:
        # 添加應用程式根目錄到 Python 路徑
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # 設置環境
        setup_environment()
        
        # 初始化應用
        app = QApplication(sys.argv)
        
        # 設置應用程式樣式
        app.setStyle('Fusion')
        
        # 導入主視窗 (延遲導入確保設置已完成)
        from app.ui.main_window import MainWindow
        
        # 創建並顯示主視窗
        window = MainWindow()
        global_main_window = window  # 保存到全局變量
        window.show()
        
        # 運行應用
        sys.exit(app.exec_())
    except Exception as e:
        show_error_and_exit("應用程序啟動時發生錯誤", e) 