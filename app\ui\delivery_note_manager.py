from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                             QLabel, QLineEdit, QComboBox, QFrame, QToolBar, QAction,
                             QDateEdit, QFormLayout, QGroupBox)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager

class DeliveryNoteManager(QWidget):
    def __init__(self, db_manager, main_window=None, permission_mgr=None):
        super().__init__()
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        self.main_window = main_window  # 存儲主窗口引用
        self.permission_mgr = permission_mgr  # 儲存權限管理器引用
        self.init_ui()
        # 初始化時加載真實數據
        self.load_delivery_notes()
        
        # 設置權限
        self.setup_permissions()
        
    def setup_permissions(self):
        """根據用戶角色設置界面權限"""
        if not self.permission_mgr:
            return
            
        # 設置修改按鈕的權限
        modify_buttons = [self.btn_add, self.btn_edit, self.btn_delete]
        
        # 為修改按鈕設置類型標記
        for btn in modify_buttons:
            btn.action_type = 'modify'
            
        # 更新按鈕啟用狀態
        self.permission_mgr.update_ui_for_role(buttons=modify_buttons)
        
    def init_ui(self):
        self.setWindowTitle(self.lang_manager.get_text('delivery_note'))
        main_layout = QVBoxLayout(self)
        
        # 添加頁面標題
        title_layout = QHBoxLayout()
        title_label = QLabel(self.lang_manager.get_text('delivery_note'))
        title_label.setObjectName("page-title")
        title_label.setFont(title_label.font())
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 搜索和過濾區域
        filter_group = QGroupBox(self.lang_manager.get_text('search_and_filter'))
        filter_layout = QFormLayout(filter_group)
        
        # 水平布局用於搜索
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        search_btn = QPushButton(self.lang_manager.get_text('search'))
        search_btn.setIcon(self.icon_manager.get_icon('search'))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        filter_layout.addRow(self.lang_manager.get_text('search_criteria'), search_layout)
        
        # 日期過濾器
        date_layout = QHBoxLayout()
        self.date_from = QDateEdit(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit(QDate.currentDate())
        self.date_from.setCalendarPopup(True)
        self.date_to.setCalendarPopup(True)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('from')))
        date_layout.addWidget(self.date_from)
        date_layout.addWidget(QLabel(self.lang_manager.get_text('to')))
        date_layout.addWidget(self.date_to)
        filter_layout.addRow(self.lang_manager.get_text('date_range'), date_layout)
        
        # 送貨狀態過濾器
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.lang_manager.get_text('all_status'))
        self.status_filter.addItem(self.lang_manager.get_text('preparing'))
        self.status_filter.addItem(self.lang_manager.get_text('shipped'))
        self.status_filter.addItem(self.lang_manager.get_text('delivered'))
        filter_layout.addRow(self.lang_manager.get_text('delivery_status'), self.status_filter)
        
        main_layout.addWidget(filter_group)
        
        # 按鈕工具列 - 使用QHBoxLayout代替QToolBar，以實現更好的佈局控制
        buttons_layout = QHBoxLayout()
        
        # 創建分組容器以美化佈局
        basic_actions = QGroupBox("")
        basic_actions.setFlat(True)
        basic_layout = QHBoxLayout(basic_actions)
        basic_layout.setContentsMargins(0, 0, 0, 0)
        basic_layout.setSpacing(2)
        
        # 基本操作按鈕
        self.btn_add = QPushButton(self.lang_manager.get_text('add_delivery_note'))
        self.btn_add.setIcon(self.icon_manager.get_icon('add'))
        self.btn_add.setMinimumWidth(120)
        
        self.btn_edit = QPushButton(self.lang_manager.get_text('edit_delivery_note'))
        self.btn_edit.setIcon(self.icon_manager.get_icon('edit'))
        self.btn_edit.setMinimumWidth(120)
        
        self.btn_delete = QPushButton(self.lang_manager.get_text('delete_delivery_note'))
        self.btn_delete.setIcon(self.icon_manager.get_icon('delete'))
        self.btn_delete.setMinimumWidth(120)
        
        self.btn_view = QPushButton(self.lang_manager.get_text('view_delivery_note'))
        self.btn_view.setIcon(self.icon_manager.get_icon('view'))
        self.btn_view.setMinimumWidth(120)
        
        basic_layout.addWidget(self.btn_add)
        basic_layout.addWidget(self.btn_edit)
        basic_layout.addWidget(self.btn_delete)
        basic_layout.addWidget(self.btn_view)
        buttons_layout.addWidget(basic_actions)
        
        # 垂直分隔線
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        buttons_layout.addWidget(separator_line)
        
        # 輸出操作按鈕組
        output_actions = QGroupBox("")
        output_actions.setFlat(True)
        output_layout = QHBoxLayout(output_actions)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(2)
        
        self.btn_print = QPushButton(self.lang_manager.get_text('print'))
        self.btn_print.setIcon(self.icon_manager.get_icon('print'))
        self.btn_print.setMinimumWidth(80)
        
        self.btn_export = QPushButton(self.lang_manager.get_text('export'))
        self.btn_export.setIcon(self.icon_manager.get_icon('export'))
        self.btn_export.setMinimumWidth(80)
        
        output_layout.addWidget(self.btn_print)
        output_layout.addWidget(self.btn_export)
        buttons_layout.addWidget(output_actions)
        
        # 添加彈性空間，使按鈕靠左對齊
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # 添加一條分隔線
        bottom_separator = QFrame()
        bottom_separator.setFrameShape(QFrame.HLine)
        bottom_separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(bottom_separator)
        
        # 送貨單表格
        self.table = QTableWidget(0, 6)  # ID, 送貨單號, 客戶, 送貨日期, 送貨地址, 送貨狀態
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('delivery_note_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('delivery_date'),
            self.lang_manager.get_text('delivery_address'),
            self.lang_manager.get_text('delivery_status')
        ])
        
        # 隱藏ID列
        self.table.setColumnHidden(0, True)
        
        # 表格佈局
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(self.table.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        main_layout.addWidget(self.table)
        
        # 狀態標籤
        self.status_label = QLabel(f"{self.lang_manager.get_text('total_records')}: 0")
        main_layout.addWidget(self.status_label)
        
        # 連接按鈕事件
        self.btn_add.clicked.connect(self.add_delivery_note)
        self.btn_edit.clicked.connect(self.edit_delivery_note)
        self.btn_delete.clicked.connect(self.delete_delivery_note)
        self.btn_view.clicked.connect(self.view_delivery_note)
        self.btn_print.clicked.connect(self.print_delivery_note)
        self.btn_export.clicked.connect(self.export_delivery_note)
        
        search_btn.clicked.connect(self.search_delivery_note)
        self.status_filter.currentIndexChanged.connect(self.filter_by_status)
        
    def load_delivery_notes(self, search_text="", date_from=None, date_to=None, status=None):
        """從數據庫加載送貨單數據"""
        if self.db_manager:
            # 獲取送貨單列表
            delivery_notes = self.db_manager.get_delivery_notes(search_text, date_from, date_to, status)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            for note in delivery_notes:
                row = self.table.rowCount()
                self.table.insertRow(row)
                
                # ID列
                self.table.setItem(row, 0, QTableWidgetItem(str(note.get('id', ''))))
                
                # 送貨單號
                self.table.setItem(row, 1, QTableWidgetItem(note.get('delivery_note_no', '')))
                
                # 客戶
                self.table.setItem(row, 2, QTableWidgetItem(note.get('customer_name', '')))
                
                # 送貨日期
                self.table.setItem(row, 3, QTableWidgetItem(note.get('delivery_date', '')))
                
                # 送貨地址
                self.table.setItem(row, 4, QTableWidgetItem(note.get('delivery_address', '')))
                
                # 送貨狀態
                self.table.setItem(row, 5, QTableWidgetItem(note.get('delivery_status', '')))
            
            # 更新狀態標籤
            self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}")
    
    def add_delivery_note(self):
        """新增送貨單"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
        # 成功添加後應該刷新列表
        self.load_delivery_notes()
    
    def edit_delivery_note(self):
        """編輯送貨單"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
        # 成功編輯後應該刷新列表
        self.load_delivery_notes()
    
    def delete_delivery_note(self):
        """刪除送貨單"""
        # 檢查權限
        if self.permission_mgr and not self.permission_mgr.check_modify_permission():
            return
            
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取送貨單ID和編號
        row = selected_rows[0].row()
        delivery_id = int(self.table.item(row, 0).text())
        delivery_no = self.table.item(row, 1).text()
        
        # 確認刪除
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            f"確認要刪除送貨單 {delivery_no} 嗎？此操作不可撤銷。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 獲取當前用戶信息
            deleted_by = "system"
            if self.main_window and hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                deleted_by = self.main_window.current_user.get('username', 'system')
            
            # 刪除送貨單（移動到回收站）
            if self.db_manager.delete_delivery_note(delivery_id, deleted_by):
                # 刪除成功，重新加載送貨單數據
                self.load_delivery_notes()
                QMessageBox.information(
                    self,
                    self.lang_manager.get_text('success'),
                    "送貨單已移動到回收站"
                )
            else:
                # 刪除失敗
                QMessageBox.critical(
                    self,
                    self.lang_manager.get_text('error'),
                    self.lang_manager.get_text('operation_failed')
                )
    
    def view_delivery_note(self):
        """查看送貨單"""
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def print_delivery_note(self):
        """打印送貨單"""
        QMessageBox.information(self, self.lang_manager.get_text('tip'), 
                              self.lang_manager.get_text('function_not_implemented'))
    
    def export_delivery_note(self):
        """匯出送貨單"""
        # 獲取所選行
        selected_rows = self.table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select')
            )
            return
        
        # 獲取送貨單ID
        row = selected_rows[0].row()
        delivery_note_id = int(self.table.item(row, 0).text())
        
        # 從數據庫獲取送貨單數據
        delivery_note_data = self.db_manager.get_delivery_note(delivery_note_id)
        
        if not delivery_note_data:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                self.lang_manager.get_text('delivery_note_not_found')
            )
            return
            
        try:
            # 準備默認文件名
            default_filename = f"DeliveryNote_{delivery_note_data['delivery_note_no'].replace('/', '_')}.pdf"
            
            # 顯示文件保存對話框
            from PyQt5.QtWidgets import QFileDialog
            output_path, _ = QFileDialog.getSaveFileName(
                self,
                self.lang_manager.get_text('export_pdf'),
                default_filename,
                "PDF Files (*.pdf)"
            )
            
            # 如果用戶取消，則返回
            if not output_path:
                return
            
            # 使用統一的PDF管理器
            from app.utils.pdf_manager import PDFManager
            pdf_manager = PDFManager()
            pdf_file = pdf_manager.generate_delivery_note_pdf(delivery_note_data, output_path)
            
            if pdf_file:
                # 顯示成功消息
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    f"{self.lang_manager.get_text('export_success')}\n{pdf_file}"
                )
        except ImportError:
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('information'),
                self.lang_manager.get_text('export_pdf_not_implemented')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('export_pdf_error')}: {str(e)}"
            )
    
    def search_delivery_note(self):
        """按條件搜索送貨單"""
        search_text = self.search_input.text().strip()
        date_from = self.date_from.date().toString("yyyy-MM-dd")
        date_to = self.date_to.date().toString("yyyy-MM-dd")
        status = self.status_filter.currentText()
        
        # 如果是"所有狀態"，則將status設為None
        if status == self.lang_manager.get_text('all_status'):
            status = None
        
        # 從數據庫加載過濾後的送貨單
        self.load_delivery_notes(search_text, date_from, date_to, status)
    
    def filter_by_status(self):
        """按狀態過濾送貨單"""
        # 重用搜索功能，但只更改狀態過濾
        self.search_delivery_note()
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()
        self.setWindowTitle(self.lang_manager.get_text('delivery_note'))
        
        # 更新搜索區域
        self.search_input.setPlaceholderText(self.lang_manager.get_text('search'))
        
        # 更新按鈕文字
        self.btn_add.setText(self.lang_manager.get_text('add_delivery_note'))
        self.btn_edit.setText(self.lang_manager.get_text('edit_delivery_note'))
        self.btn_delete.setText(self.lang_manager.get_text('delete_delivery_note'))
        self.btn_view.setText(self.lang_manager.get_text('view_delivery_note'))
        self.btn_print.setText(self.lang_manager.get_text('print'))
        self.btn_export.setText(self.lang_manager.get_text('export'))
        
        # 更新表格標題
        self.table.setHorizontalHeaderLabels([
            'ID',
            self.lang_manager.get_text('delivery_note_no'),
            self.lang_manager.get_text('customer'),
            self.lang_manager.get_text('delivery_date'),
            self.lang_manager.get_text('delivery_address'),
            self.lang_manager.get_text('delivery_status')
        ])
        
        # 更新狀態標籤
        self.status_label.setText(f"{self.lang_manager.get_text('total_records')}: {self.table.rowCount()}") 