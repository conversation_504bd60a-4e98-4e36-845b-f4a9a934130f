from PyQt5.QtGui import QIcon
from PyQt5.QtCore import QSize
import os

class IconManager:
    """圖標管理器類，用於統一管理應用程序中的圖標"""
    
    @staticmethod
    def get_icon(icon_name):
        """獲取指定名稱的圖標，如果不存在則返回空圖標"""
        path = f"app/assets/icons/{icon_name}.png"
        if os.path.exists(path):
            return QIcon(path)
        else:
            # 返回空圖標
            return QIcon()
    
    @staticmethod
    def get_icon_names():
        """返回所有可用的圖標名稱及其用途"""
        return {
            "quotation": "報價單模塊",
            "invoice": "發票模塊",
            "delivery": "送貨單模塊",
            "customer": "客戶管理",
            "product": "產品管理",
            "settings": "系統設定",
            "user": "用戶管理",
            "add": "添加操作",
            "edit": "編輯操作",
            "delete": "刪除操作",
            "view": "查看詳情",
            "save": "保存",
            "cancel": "取消",
            "search": "搜索",
            "language": "語言切換",
            "export": "導出",
            "print": "打印",
            "company": "公司信息",
            "logout": "登出系統",
            "backup": "數據備份",
            "restore": "數據還原",
            "database": "數據庫管理"
        } 