#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_reportlab_installation():
    """測試ReportLab是否正確安裝"""
    try:
        import reportlab
        print("✅ ReportLab已安裝")
        print(f"   版本: {reportlab.Version}")
        return True
    except ImportError as e:
        print(f"❌ ReportLab未安裝: {e}")
        print("   請運行: pip install reportlab")
        return False

def test_simple_pdf_generator():
    """測試簡單PDF生成器"""
    try:
        from app.utils.pdf_generator_simple import PDFGeneratorSimple
        print("✅ 成功導入簡單PDF生成器")
        
        # 創建生成器實例
        pdf_generator = PDFGeneratorSimple()
        print("✅ 簡單PDF生成器創建成功")
        
        # 測試數據
        test_quotation_data = {
            'quotation_no': 'SIMPLE-TEST-001',
            'date': 'MAY 28, 25',
            'valid_until': 'JUNE 28, 25',
            'customer_name': 'SIMPLE PDF TEST COMPANY LIMITED',
            'contact_person': 'MR. SIMPLE PDF TESTER',
            'address': 'Simple PDF Test Address, Professional Building, Test District',
            'phone': '+852 2345 6789',
            'fax': '+852 2345 6790',
            'sales_person': 'SIMPLE SALES',
            'contract_no': 'HB-ENG-2025-SIMPLE',
            'contract_description': 'SIMPLE PDF GENERATION TEST CONTRACT',
            'items': [
                {
                    'item_no': 1,
                    'model': 'SIMPLE-MODEL-001',
                    'description': 'Simple PDF test item with ReportLab generation capabilities, providing stable and reliable PDF output for business documents',
                    'quantity': 2.0,
                    'unit': 'SET',
                    'unit_price': 3000.00,
                    'discount': 0,
                    'amount': 6000.00
                },
                {
                    'item_no': 2,
                    'model': 'SIMPLE-MODEL-002',
                    'description': 'Advanced ReportLab formatting test item with professional layout and business-standard styling',
                    'quantity': 1.0,
                    'unit': 'SYSTEM',
                    'unit_price': 8000.00,
                    'discount': 10,
                    'amount': 7200.00
                },
                {
                    'item_no': 3,
                    'model': 'SIMPLE-MODEL-003',
                    'description': 'Multi-item test for verifying table layout and pricing calculations in ReportLab PDF generation',
                    'quantity': 3.0,
                    'unit': 'SET',
                    'unit_price': 2500.00,
                    'discount': 5,
                    'amount': 7125.00
                }
            ],
            'total_amount': 20325.00,
            'terms': '30% DEPOSIT, 70% UPON COMPLETION',
            'delivery_terms': 'APPROX. 5 WEEKS',
            'pricing_terms': 'C.I.F. HONG KONG',
            'remarks': 'This is a Simple PDF test using ReportLab for reliable PDF generation.'
        }
        
        print("🔄 正在生成簡單PDF...")
        print(f"📊 項目數量: {len(test_quotation_data['items'])}")
        print(f"💰 總金額: HK$ {test_quotation_data['total_amount']:,.2f}")
        
        # 生成報價單PDF
        quotation_pdf = pdf_generator.generate_quotation_pdf(test_quotation_data)
        
        if quotation_pdf and os.path.exists(quotation_pdf):
            file_size = os.path.getsize(quotation_pdf)
            print(f"✅ 報價單PDF生成成功: {quotation_pdf}")
            print(f"📄 文件大小: {file_size:,} bytes")
        else:
            print("❌ 報價單PDF生成失敗")
            return False
        
        # 生成發票PDF
        invoice_data = test_quotation_data.copy()
        invoice_data['invoice_no'] = 'SIMPLE-INV-001'
        invoice_pdf = pdf_generator.generate_invoice_pdf(invoice_data)
        
        if invoice_pdf and os.path.exists(invoice_pdf):
            print(f"✅ 發票PDF生成成功: {invoice_pdf}")
        else:
            print("❌ 發票PDF生成失敗")
            return False
        
        # 生成送貨單PDF
        delivery_data = test_quotation_data.copy()
        delivery_data['delivery_note_no'] = 'SIMPLE-DN-001'
        delivery_pdf = pdf_generator.generate_delivery_note_pdf(delivery_data)
        
        if delivery_pdf and os.path.exists(delivery_pdf):
            print(f"✅ 送貨單PDF生成成功: {delivery_pdf}")
        else:
            print("❌ 送貨單PDF生成失敗")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_manager_with_multiple_generators():
    """測試PDF管理器的多生成器支持"""
    try:
        from app.utils.pdf_manager import PDFManager
        print("\n🔄 測試PDF管理器（多生成器支持）...")
        
        # 創建PDF管理器
        pdf_manager = PDFManager()
        print("✅ PDF管理器創建成功")
        
        # 獲取生成器信息
        info = pdf_manager.get_generator_info()
        print(f"📋 當前生成器: {info['type']}")
        print(f"   類別: {info['class']}")
        print(f"   描述: {info['description']}")
        print(f"   可用: {info['available']}")
        
        # 獲取所有可用生成器
        available_generators = pdf_manager.get_available_generators()
        print(f"🔧 可用生成器: {', '.join(available_generators)}")
        
        # 顯示安裝指南
        guide = pdf_manager.get_installation_guide()
        print(f"📖 安裝狀態: {guide}")
        
        # 測試生成功能
        test_result = pdf_manager.test_generation()
        if test_result:
            print("✅ PDF管理器測試通過")
            return True
        else:
            print("❌ PDF管理器測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ PDF管理器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_large_document():
    """測試大型文檔生成"""
    try:
        from app.utils.pdf_generator_simple import PDFGeneratorSimple
        pdf_generator = PDFGeneratorSimple()
        
        print("\n🔄 測試大型文檔生成...")
        
        # 創建包含多個項目的測試數據
        items = []
        for i in range(20):  # 20個項目
            items.append({
                'item_no': i + 1,
                'model': f'LARGE-DOC-{i+1:03d}',
                'description': f'Large document test item {i+1} with detailed description for testing ReportLab performance and layout consistency. This item demonstrates the stable PDF generation capabilities of the simple generator.',
                'quantity': 1.0 + (i % 4),
                'unit': 'SET',
                'unit_price': 1500.00 + (i * 300),
                'discount': i % 6,
                'amount': (1.0 + (i % 4)) * (1500.00 + (i * 300)) * (1 - (i % 6) / 100)
            })
        
        large_doc_data = {
            'quotation_no': 'SIMPLE-LARGE-001',
            'date': 'MAY 28, 25',
            'valid_until': 'JUNE 28, 25',
            'customer_name': 'LARGE DOCUMENT TEST COMPANY LIMITED',
            'contact_person': 'MR. LARGE DOC TESTER',
            'address': 'Large Document Test Address, Professional Building, Test District',
            'phone': '+852 8888 0000',
            'fax': '+852 8888 0001',
            'sales_person': 'LARGE DOC SALES',
            'contract_no': 'HB-ENG-2025-LARGE',
            'contract_description': 'LARGE DOCUMENT PDF GENERATION TEST CONTRACT',
            'items': items,
            'total_amount': sum(item['amount'] for item in items),
            'terms': '20% DEPOSIT, 80% UPON COMPLETION',
            'delivery_terms': 'APPROX. 10 WEEKS',
            'pricing_terms': 'C.I.F. HONG KONG',
            'remarks': 'This is a large document test to verify ReportLab performance with many items.'
        }
        
        print(f"📊 項目數量: {len(large_doc_data['items'])}")
        print(f"💰 總金額: HK$ {large_doc_data['total_amount']:,.2f}")
        
        # 生成大型文檔PDF
        large_pdf = pdf_generator.generate_quotation_pdf(large_doc_data)
        
        if large_pdf and os.path.exists(large_pdf):
            file_size = os.path.getsize(large_pdf)
            print(f"✅ 大型文檔PDF生成成功: {large_pdf}")
            print(f"📄 文件大小: {file_size:,} bytes")
            return True
        else:
            print("❌ 大型文檔PDF生成失敗")
            return False
            
    except Exception as e:
        print(f"❌ 大型文檔測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試簡單PDF生成器...")
    print("=" * 60)
    
    # 測試ReportLab安裝
    reportlab_installed = test_reportlab_installation()
    if not reportlab_installed:
        print("\n💡 安裝說明:")
        print("   pip install reportlab")
        return False
    
    print()
    
    # 測試基本功能
    basic_test = test_simple_pdf_generator()
    
    # 測試PDF管理器
    manager_test = test_pdf_manager_with_multiple_generators()
    
    # 測試大型文檔
    large_doc_test = test_large_document()
    
    print("\n" + "=" * 60)
    
    if basic_test and manager_test and large_doc_test:
        print("🎉 所有測試通過！")
        print("📝 簡單PDF生成器已成功配置")
        print("🔍 請檢查output目錄中的PDF文件")
        print("\n💡 簡單PDF生成器優勢:")
        print("   ✅ 無複雜依賴，易於安裝")
        print("   ✅ 穩定可靠的ReportLab引擎")
        print("   ✅ 適合Windows環境")
        print("   ✅ 專業的商業文檔格式")
        print("   ✅ 良好的性能表現")
        print("   ✅ 自動多頁面支持")
        return True
    else:
        print("💥 部分測試失敗！")
        print("請檢查錯誤信息並解決問題")
        return False

if __name__ == "__main__":
    success = main()
    print("\n按任意鍵退出...")
    input()
    sys.exit(0 if success else 1) 