from PyQt5.QtWidgets import QMessageBox, QInputDialog
from app.utils.logger import system_logger

class DialogFactory:
    """
    對話框工廠類，負責統一管理對話框的創建和顯示
    實現對話框樣式和行為的一致性
    記錄對話框的顯示和用戶交互
    """
    
    @staticmethod
    def create_message_box(parent, title, message, icon=QMessageBox.Information, 
                          buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):
        """
        創建並顯示一個消息對話框
        
        參數:
            parent: 父窗口
            title: 對話框標題
            message: 顯示信息
            icon: 信息圖標類型
            buttons: 顯示的按鈕
            default_button: 默認按鈕
            
        返回:
            用戶點擊的按鈕
        """
        system_logger.debug(f"顯示消息對話框: {title} - {message}")
        
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStandardButtons(buttons)
        msg_box.setDefaultButton(default_button)
        
        # 設置樣式
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #f8f8f8;
            }
            QPushButton {
                padding: 5px 15px;
                border-radius: 4px;
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
        """)
        
        result = msg_box.exec_()
        system_logger.debug(f"消息對話框結果: {result}")
        return result
    
    @staticmethod
    def create_info_dialog(parent, title, message):
        """創建信息對話框"""
        return DialogFactory.create_message_box(
            parent, title, message, QMessageBox.Information
        )
    
    @staticmethod
    def create_warning_dialog(parent, title, message):
        """創建警告對話框"""
        return DialogFactory.create_message_box(
            parent, title, message, QMessageBox.Warning
        )
    
    @staticmethod
    def create_error_dialog(parent, title, message):
        """創建錯誤對話框"""
        system_logger.error(f"錯誤對話框: {title} - {message}")
        return DialogFactory.create_message_box(
            parent, title, message, QMessageBox.Critical
        )
    
    @staticmethod
    def create_confirm_dialog(parent, title, message, default_yes=True):
        """創建確認對話框，返回用戶是否確認"""
        buttons = QMessageBox.Yes | QMessageBox.No
        default = QMessageBox.Yes if default_yes else QMessageBox.No
        result = DialogFactory.create_message_box(
            parent, title, message, QMessageBox.Question, buttons, default
        )
        return result == QMessageBox.Yes
    
    @staticmethod
    def create_input_dialog(parent, title, label, default_text=""):
        """創建輸入對話框，返回用戶輸入內容和是否確認"""
        system_logger.debug(f"顯示輸入對話框: {title} - {label}")
        
        dialog = QInputDialog(parent)
        dialog.setWindowTitle(title)
        dialog.setLabelText(label)
        dialog.setTextValue(default_text)
        
        # 設置樣式
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #f8f8f8;
            }
            QPushButton {
                padding: 5px 15px;
                border-radius: 4px;
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
            }
            QLineEdit {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
        """)
        
        result = dialog.exec_()
        text = dialog.textValue()
        
        system_logger.debug(f"輸入對話框結果: {'確認' if result else '取消'}, 輸入: {text}")
        return text, result == QInputDialog.Accepted
    
    @staticmethod
    def create_custom_dialog(dialog_class, parent=None, **kwargs):
        """
        創建並顯示自定義對話框
        
        參數:
            dialog_class: 對話框類
            parent: 父窗口
            kwargs: 傳遞給對話框的參數
            
        返回:
            對話框對象和執行結果
        """
        dialog_name = dialog_class.__name__
        system_logger.debug(f"創建自定義對話框: {dialog_name}")
        
        try:
            dialog = dialog_class(parent, **kwargs)
            result = dialog.exec_()
            
            system_logger.debug(f"對話框 {dialog_name} 結果: {result}")
            return dialog, result
        except Exception as e:
            system_logger.error(f"創建對話框 {dialog_name} 失敗", exc_info=True)
            DialogFactory.create_error_dialog(
                parent, 
                "對話框錯誤", 
                f"創建對話框 {dialog_name} 時出錯: {str(e)}"
            )
            return None, None 