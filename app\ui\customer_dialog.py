from PyQt5.QtWidgets import QDialog, QFormLayout, QLineEdit, QPushButton, QHBoxLayout, QVBoxLayout
from app.utils.language_manager import LanguageManager

class CustomerDialog(QDialog):
    def __init__(self, parent=None, customer_data=None):
        super().__init__(parent)
        self.customer_data = customer_data  # 如果為None則是新增，否則是編輯
        self.lang_manager = LanguageManager()
        self.init_ui()
        
        # 如果是編輯模式，填充表單
        if self.customer_data:
            self.fill_form()
            
    def init_ui(self):
        title = self.lang_manager.get_text('edit_customer') if self.customer_data else self.lang_manager.get_text('add_customer')
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        
        # 表單佈局
        form_layout = QFormLayout()
        
        # 客戶名稱
        self.name_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('customer_name'), self.name_edit)
        
        # 聯絡人
        self.contact_person_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('contact_person'), self.contact_person_edit)
        
        # 電話
        self.phone_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('phone'), self.phone_edit)
        
        # 電郵
        self.email_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('email'), self.email_edit)
        
        # 地址
        self.address_edit = QLineEdit()
        form_layout.addRow(self.lang_manager.get_text('address'), self.address_edit)
        
        layout.addLayout(form_layout)
        
        # 按鈕佈局
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton(self.lang_manager.get_text('save'))
        self.cancel_btn = QPushButton(self.lang_manager.get_text('cancel'))
        
        btn_layout.addStretch()
        btn_layout.addWidget(self.save_btn)
        btn_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(btn_layout)
        
        # 連接信號
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
    def fill_form(self):
        """用現有客戶數據填充表單"""
        self.name_edit.setText(self.customer_data[1])
        self.contact_person_edit.setText(self.customer_data[2])
        self.phone_edit.setText(self.customer_data[3])
        self.email_edit.setText(self.customer_data[4])
        self.address_edit.setText(self.customer_data[5])
        
    def get_data(self):
        """獲取用戶輸入的數據"""
        return {
            'name': self.name_edit.text(),
            'contact_person': self.contact_person_edit.text(),
            'phone': self.phone_edit.text(),
            'email': self.email_edit.text(),
            'address': self.address_edit.text()
        } 