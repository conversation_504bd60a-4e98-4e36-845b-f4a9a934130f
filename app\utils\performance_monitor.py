"""
性能監控工具
監控應用程式的性能指標，包括執行時間、內存使用等
"""

import time
import psutil
import functools
from app.utils.logger import system_logger

class PerformanceMonitor:
    """性能監控類"""
    
    def __init__(self):
        self.start_time = time.time()
        self.process = psutil.Process()
        
    @staticmethod
    def measure_time(func):
        """測量函數執行時間的裝飾器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 記錄執行時間
                if execution_time > 1.0:  # 只記錄超過1秒的操作
                    system_logger.info(f"性能監控 - {func.__name__} 執行時間: {execution_time:.2f}秒")
                elif execution_time > 0.1:  # 超過100ms的操作記錄為調試信息
                    system_logger.debug(f"性能監控 - {func.__name__} 執行時間: {execution_time:.3f}秒")
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                system_logger.error(f"性能監控 - {func.__name__} 執行失敗，耗時: {execution_time:.3f}秒，錯誤: {str(e)}")
                raise
        return wrapper
    
    def get_memory_usage(self):
        """獲取當前內存使用情況"""
        try:
            memory_info = self.process.memory_info()
            return {
                'rss': memory_info.rss / 1024 / 1024,  # MB
                'vms': memory_info.vms / 1024 / 1024,  # MB
                'percent': self.process.memory_percent()
            }
        except Exception as e:
            system_logger.error(f"獲取內存使用情況失敗: {str(e)}")
            return None
    
    def get_cpu_usage(self):
        """獲取CPU使用率"""
        try:
            return self.process.cpu_percent()
        except Exception as e:
            system_logger.error(f"獲取CPU使用率失敗: {str(e)}")
            return None
    
    def log_system_stats(self):
        """記錄系統統計信息"""
        try:
            memory = self.get_memory_usage()
            cpu = self.get_cpu_usage()
            uptime = time.time() - self.start_time
            
            if memory and cpu is not None:
                system_logger.info(
                    f"系統狀態 - 運行時間: {uptime:.1f}秒, "
                    f"內存使用: {memory['rss']:.1f}MB ({memory['percent']:.1f}%), "
                    f"CPU使用率: {cpu:.1f}%"
                )
        except Exception as e:
            system_logger.error(f"記錄系統統計信息失敗: {str(e)}")

# 全局性能監控器實例
performance_monitor = PerformanceMonitor() 