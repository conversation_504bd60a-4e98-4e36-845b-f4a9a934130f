from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLabel, QLineEdit, QDateEdit, QComboBox, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                             QSpinBox, QDoubleSpinBox, QGroupBox, QTabWidget, QTextEdit,
                             QToolButton, QCheckBox, QCompleter, QFrame, QWidget, QRadioButton)
from PyQt5.QtCore import Qt, QDate, QDateTime, QStringListModel
from PyQt5.QtGui import QFont
from app.utils.language_manager import LanguageManager
from app.utils.icon_manager import IconManager

class QuotationDialog(QDialog):
    """報價單新增/編輯對話框"""
    
    def __init__(self, parent=None, db_manager=None, quotation_data=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.quotation_data = quotation_data  # 如果提供，則為編輯模式
        self.lang_manager = LanguageManager()
        self.icon_manager = IconManager()
        
        self.item_data = []  # 用於存儲報價單項目
        
        self.init_ui()
        
        # 如果是編輯模式，載入報價單數據
        if self.quotation_data:
            self.load_quotation_data()
        else:
            # 新建模式，設置默認值
            self.setup_default_values()
    
    def init_ui(self):
        """初始化界面"""
        # 設置窗口屬性
        self.setWindowTitle(self.lang_manager.get_text('add_quotation') if not self.quotation_data else self.lang_manager.get_text('edit_quotation'))
        self.setMinimumSize(900, 700)
        
        # 主佈局
        main_layout = QVBoxLayout(self)
        
        # 標籤頁控件
        tab_widget = QTabWidget()
        
        # ====== 標籤頁1：基本信息 ======
        basic_info_tab = QWidget()
        basic_layout = QVBoxLayout(basic_info_tab)
        
        # 報價單號和日期區域
        header_group = QGroupBox(self.lang_manager.get_text('quotation_details'))
        header_layout = QFormLayout(header_group)
        
        # 報價單號 - 自動生成，但允許修改
        self.quotation_no_edit = QLineEdit()
        self.quotation_no_edit.setPlaceholderText(self.lang_manager.get_text('auto_generate'))
        header_layout.addRow(self.lang_manager.get_text('quotation_no'), self.quotation_no_edit)
        
        # 報價日期
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        header_layout.addRow(self.lang_manager.get_text('date'), self.date_edit)
        
        # 有效期至
        self.valid_until_edit = QDateEdit(QDate.currentDate().addMonths(1))
        self.valid_until_edit.setCalendarPopup(True)
        header_layout.addRow(self.lang_manager.get_text('valid_until'), self.valid_until_edit)
        
        # 狀態
        self.status_combo = QComboBox()
        self.status_combo.addItem(self.lang_manager.get_text('draft'))
        self.status_combo.addItem(self.lang_manager.get_text('sent'))
        self.status_combo.addItem(self.lang_manager.get_text('approved'))
        self.status_combo.addItem(self.lang_manager.get_text('rejected'))
        header_layout.addRow(self.lang_manager.get_text('status'), self.status_combo)
        
        basic_layout.addWidget(header_group)
        
        # 客戶信息區域
        customer_group = QGroupBox(self.lang_manager.get_text('customer_info'))
        customer_layout = QFormLayout(customer_group)
        
        # 客戶選擇下拉框
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        self.customer_combo.setMinimumWidth(250)
        customer_layout.addRow(self.lang_manager.get_text('customer'), self.customer_combo)
        
        # 聯繫人
        self.contact_edit = QLineEdit()
        customer_layout.addRow(self.lang_manager.get_text('contact_person'), self.contact_edit)
        
        # 電話
        self.phone_edit = QLineEdit()
        customer_layout.addRow(self.lang_manager.get_text('phone'), self.phone_edit)
        
        # 地址
        self.address_edit = QLineEdit()
        customer_layout.addRow(self.lang_manager.get_text('address'), self.address_edit)
        
        basic_layout.addWidget(customer_group)
        
        # 添加到標籤頁
        tab_widget.addTab(basic_info_tab, self.lang_manager.get_text('basic_info'))
        
        # ====== 標籤頁2：項目清單 ======
        items_tab = QWidget()
        items_layout = QVBoxLayout(items_tab)
        
        # 工具欄
        items_toolbar_layout = QHBoxLayout()
        
        self.btn_add_item = QPushButton(self.lang_manager.get_text('add_item'))
        self.btn_add_item.setIcon(self.icon_manager.get_icon('add'))
        self.btn_edit_item = QPushButton(self.lang_manager.get_text('edit_item'))
        self.btn_edit_item.setIcon(self.icon_manager.get_icon('edit'))
        self.btn_delete_item = QPushButton(self.lang_manager.get_text('delete_item'))
        self.btn_delete_item.setIcon(self.icon_manager.get_icon('delete'))
        
        items_toolbar_layout.addWidget(self.btn_add_item)
        items_toolbar_layout.addWidget(self.btn_edit_item)
        items_toolbar_layout.addWidget(self.btn_delete_item)
        items_toolbar_layout.addStretch()
        
        items_layout.addLayout(items_toolbar_layout)
        
        # 項目表格
        self.items_table = QTableWidget(0, 7)  # 編號、描述、數量、單位、單價、折扣、小計
        self.items_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('item_no'),
            self.lang_manager.get_text('description'),
            self.lang_manager.get_text('quantity'),
            self.lang_manager.get_text('unit'),
            self.lang_manager.get_text('unit_price'),
            self.lang_manager.get_text('discount'),
            self.lang_manager.get_text('amount')
        ])
        
        # 表格佈局
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # 描述列自適應寬度
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        items_layout.addWidget(self.items_table)
        
        # 合計區域
        totals_layout = QFormLayout()
        
        # 小計
        self.subtotal_label = QLabel("0.00")
        self.subtotal_label.setAlignment(Qt.AlignRight)
        totals_layout.addRow(self.lang_manager.get_text('subtotal'), self.subtotal_label)
        
        # 折扣
        discount_layout = QHBoxLayout()
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix("%")
        self.discount_spin.setValue(0)
        self.discount_spin.valueChanged.connect(self.calculate_total)
        discount_layout.addWidget(self.discount_spin)
        self.discount_amount_label = QLabel("0.00")
        self.discount_amount_label.setAlignment(Qt.AlignRight)
        discount_layout.addWidget(self.discount_amount_label)
        totals_layout.addRow(self.lang_manager.get_text('discount'), discount_layout)
        
        # 總計
        self.total_label = QLabel("0.00")
        self.total_label.setAlignment(Qt.AlignRight)
        self.total_label.setFont(QFont("Arial", 10, QFont.Bold))
        totals_layout.addRow(self.lang_manager.get_text('total'), self.total_label)
        
        items_layout.addLayout(totals_layout)
        
        # 添加到標籤頁
        tab_widget.addTab(items_tab, self.lang_manager.get_text('items'))
        
        # ====== 標籤頁3：備註和條款 ======
        notes_tab = QWidget()
        notes_layout = QVBoxLayout(notes_tab)
        
        # 備註
        notes_group = QGroupBox(self.lang_manager.get_text('notes'))
        notes_group_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        notes_group_layout.addWidget(self.notes_edit)
        
        notes_layout.addWidget(notes_group)
        
        # 條款和條件
        terms_group = QGroupBox(self.lang_manager.get_text('terms_and_conditions'))
        terms_group_layout = QVBoxLayout(terms_group)
        
        self.terms_edit = QTextEdit()
        self.terms_edit.setPlaceholderText(self.lang_manager.get_text('default_terms'))
        terms_group_layout.addWidget(self.terms_edit)
        
        notes_layout.addWidget(terms_group)
        
        # 添加到標籤頁
        tab_widget.addTab(notes_tab, self.lang_manager.get_text('notes_and_terms'))
        
        # 把標籤頁添加到主佈局
        main_layout.addWidget(tab_widget)
        
        # 按鈕區域
        buttons_layout = QHBoxLayout()
        
        self.btn_save = QPushButton(self.lang_manager.get_text('save'))
        self.btn_save.setIcon(self.icon_manager.get_icon('save'))
        self.btn_preview = QPushButton(self.lang_manager.get_text('preview'))
        self.btn_export_pdf = QPushButton(self.lang_manager.get_text('export_pdf'))
        self.btn_export_pdf.setIcon(self.icon_manager.get_icon('pdf'))
        self.btn_cancel = QPushButton(self.lang_manager.get_text('cancel'))
        self.btn_cancel.setIcon(self.icon_manager.get_icon('cancel'))
        
        buttons_layout.addWidget(self.btn_preview)
        buttons_layout.addWidget(self.btn_export_pdf)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_cancel)
        
        main_layout.addLayout(buttons_layout)
        
        # 連接事件
        self.btn_add_item.clicked.connect(self.add_item)
        self.btn_edit_item.clicked.connect(self.edit_item)
        self.btn_delete_item.clicked.connect(self.delete_item)
        
        self.btn_save.clicked.connect(self.accept)
        self.btn_cancel.clicked.connect(self.reject)
        self.btn_preview.clicked.connect(self.preview_quotation)
        self.btn_export_pdf.clicked.connect(self.export_pdf)
        
        self.customer_combo.currentIndexChanged.connect(self.on_customer_changed)
        
        # 加載客戶列表
        self.load_customers()
    
    def load_customers(self):
        """加載客戶列表到下拉框"""
        if self.db_manager:
            customers = self.db_manager.get_customers()
            self.customer_combo.clear()
            
            # 添加一個空選項
            self.customer_combo.addItem("", None)
            
            # 添加客戶
            customer_names = []
            for customer in customers:
                self.customer_combo.addItem(customer['name'], customer['id'])
                customer_names.append(customer['name'])
            
            # 添加自動完成功能
            completer = QCompleter(customer_names)
            completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.customer_combo.setCompleter(completer)
    
    def on_customer_changed(self, index):
        """當客戶選擇變更時"""
        if index <= 0:  # 未選擇客戶
            self.contact_edit.clear()
            self.phone_edit.clear()
            self.address_edit.clear()
            return
            
        customer_id = self.customer_combo.itemData(index)
        customer = self.db_manager.get_customer(customer_id)
        
        if customer:
            # 使用字典格式訪問客戶資料
            self.contact_edit.setText(customer.get('contact_person', ''))
            self.phone_edit.setText(customer.get('phone', ''))
            self.address_edit.setText(customer.get('address', ''))
    
    def setup_default_values(self):
        """設置默認值"""
        # 生成新的報價單號
        if self.db_manager:
            new_quotation_no = self.db_manager.generate_new_quotation_number()
            self.quotation_no_edit.setText(new_quotation_no)
        
        # 設置默認條款
        default_terms = (
            "1. 此報價單有效期為30天。\n"
            "2. 付款條件：50%預付款，50%完工後付款。\n"
            "3. 交貨期：確認訂單後14個工作日。\n"
            "4. 所有價格均為港幣，不包含稅費。\n"
            "5. 如有任何疑問，請聯繫我們。"
        )
        self.terms_edit.setText(default_terms)
    
    def load_quotation_data(self):
        """載入報價單數據（編輯模式）"""
        if not self.quotation_data:
            return
            
        # 基本信息
        self.quotation_no_edit.setText(self.quotation_data['quotation_no'])
        self.date_edit.setDate(QDate.fromString(self.quotation_data['date'], "yyyy-MM-dd"))
        self.valid_until_edit.setDate(QDate.fromString(self.quotation_data['valid_until'], "yyyy-MM-dd"))
        
        # 設置狀態
        status_index = 0
        status_text = self.quotation_data['status']
        if status_text == self.lang_manager.get_text('draft'):
            status_index = 0
        elif status_text == self.lang_manager.get_text('sent'):
            status_index = 1
        elif status_text == self.lang_manager.get_text('approved'):
            status_index = 2
        elif status_text == self.lang_manager.get_text('rejected'):
            status_index = 3
        self.status_combo.setCurrentIndex(status_index)
        
        # 客戶信息
        customer_id = self.quotation_data['customer_id']
        for i in range(self.customer_combo.count()):
            if self.customer_combo.itemData(i) == customer_id:
                self.customer_combo.setCurrentIndex(i)
                break
        
        # 載入備註和條款
        self.notes_edit.setText(self.quotation_data.get('notes', ''))
        self.terms_edit.setText(self.quotation_data.get('terms', ''))
        
        # 載入項目
        if 'items' in self.quotation_data:
            self.item_data = self.quotation_data['items']
            self.refresh_items_table()
    
    def add_item(self):
        """添加項目"""
        dialog = QuotationItemDialog(self, self.db_manager)
        if dialog.exec_():
            item = dialog.get_data()
            # 分配項目編號
            item['item_no'] = len(self.item_data) + 1
            self.item_data.append(item)
            self.refresh_items_table()
            self.calculate_total()
    
    def edit_item(self):
        """編輯項目"""
        selected_rows = self.items_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select_item')
            )
            return
            
        row = selected_rows[0].row()
        dialog = QuotationItemDialog(self, self.db_manager, self.item_data[row])
        if dialog.exec_():
            item = dialog.get_data()
            # 保持原有的項目編號
            item['item_no'] = self.item_data[row]['item_no']
            self.item_data[row] = item
            self.refresh_items_table()
            self.calculate_total()
    
    def delete_item(self):
        """刪除項目"""
        selected_rows = self.items_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('please_select_item')
            )
            return
            
        row = selected_rows[0].row()
        
        reply = QMessageBox.question(
            self,
            self.lang_manager.get_text('confirm'),
            self.lang_manager.get_text('delete_item_confirm'),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 刪除項目
            self.item_data.pop(row)
            
            # 重新排序項目編號
            for i, item in enumerate(self.item_data):
                item['item_no'] = i + 1
                
            self.refresh_items_table()
            self.calculate_total()
    
    def refresh_items_table(self):
        """刷新項目表格"""
        self.items_table.setRowCount(0)
        
        for item in self.item_data:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)
            
            # 項目編號
            self.items_table.setItem(row, 0, QTableWidgetItem(str(item['item_no'])))
            
            # 描述
            self.items_table.setItem(row, 1, QTableWidgetItem(item['description']))
            
            # 數量
            self.items_table.setItem(row, 2, QTableWidgetItem(str(item['quantity'])))
            
            # 單位
            self.items_table.setItem(row, 3, QTableWidgetItem(item['unit']))
            
            # 單價
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            
            # 折扣
            discount_text = f"{item['discount']:.2f}%" if item['discount'] > 0 else "-"
            self.items_table.setItem(row, 5, QTableWidgetItem(discount_text))
            
            # 小計
            amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
            self.items_table.setItem(row, 6, QTableWidgetItem(f"{amount:.2f}"))
    
    def calculate_total(self):
        """計算總金額"""
        subtotal = 0
        
        for item in self.item_data:
            amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
            subtotal += amount
        
        # 顯示小計
        self.subtotal_label.setText(f"{subtotal:.2f}")
        
        # 計算整體折扣
        discount_percentage = self.discount_spin.value()
        discount_amount = subtotal * discount_percentage / 100
        self.discount_amount_label.setText(f"({discount_amount:.2f})")
        
        # 計算總計
        total = subtotal - discount_amount
        self.total_label.setText(f"{total:.2f}")
    
    def preview_quotation(self):
        """預覽報價單"""
        try:
            # 獲取表單數據
            quotation_data = self.get_data()
            
            # 使用預覽生成器
            from app.utils.preview_generator import PreviewGenerator
            preview_generator = PreviewGenerator()
            preview_generator.generate_quotation_preview(quotation_data)
        except ImportError:
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('information'),
                self.lang_manager.get_text('preview_not_implemented')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('preview_error')}: {str(e)}"
            )
    
    def get_data(self):
        """獲取對話框的數據"""
        customer_index = self.customer_combo.currentIndex()
        customer_id = self.customer_combo.itemData(customer_index) if customer_index > 0 else None
        
        # 計算總金額
        subtotal = 0
        for item in self.item_data:
            amount = item['quantity'] * item['unit_price'] * (1 - item['discount'] / 100)
            subtotal += amount
            
        discount_percentage = self.discount_spin.value()
        discount_amount = subtotal * discount_percentage / 100
        total_amount = subtotal - discount_amount
        
        data = {
            'quotation_no': self.quotation_no_edit.text(),
            'date': self.date_edit.date().toString("yyyy-MM-dd"),
            'valid_until': self.valid_until_edit.date().toString("yyyy-MM-dd"),
            'status': self.status_combo.currentText(),
            'customer_id': customer_id,
            'customer_name': self.customer_combo.currentText(),
            'contact_person': self.contact_edit.text(),
            'phone': self.phone_edit.text(),
            'address': self.address_edit.text(),
            'subtotal': subtotal,
            'discount_percentage': discount_percentage,
            'discount_amount': discount_amount,
            'total_amount': total_amount,
            'notes': self.notes_edit.toPlainText(),
            'terms': self.terms_edit.toPlainText(),
            'items': self.item_data
        }
        
        return data

    def setReadOnly(self, read_only=True):
        """設置對話框為只讀模式"""
        # 基本信息
        self.quotation_no_edit.setReadOnly(read_only)
        self.date_edit.setReadOnly(read_only)
        self.valid_until_edit.setReadOnly(read_only)
        self.status_combo.setEnabled(not read_only)
        
        # 客戶信息
        self.customer_combo.setEnabled(not read_only)
        self.contact_edit.setReadOnly(read_only)
        self.phone_edit.setReadOnly(read_only)
        self.address_edit.setReadOnly(read_only)
        
        # 項目管理按鈕
        self.btn_add_item.setVisible(not read_only)
        self.btn_edit_item.setVisible(not read_only)
        self.btn_delete_item.setVisible(not read_only)
        
        # 折扣
        self.discount_spin.setReadOnly(read_only)
        
        # 備註和條款
        self.notes_edit.setReadOnly(read_only)
        self.terms_edit.setReadOnly(read_only)
    
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        
        # 更新窗口標題
        self.setWindowTitle(self.lang_manager.get_text('add_quotation') if not self.quotation_data else self.lang_manager.get_text('edit_quotation'))
        
        # 標籤頁1：基本信息
        # 更新GroupBox標題
        for i in range(self.layout().count()):
            widget = self.layout().itemAt(i).widget()
            if isinstance(widget, QTabWidget):
                tab_widget = widget
                # 更新標籤頁標題
                tab_widget.setTabText(0, self.lang_manager.get_text('basic_info'))
                tab_widget.setTabText(1, self.lang_manager.get_text('items'))
                tab_widget.setTabText(2, self.lang_manager.get_text('notes_and_terms'))
                
                # 更新基本信息頁
                basic_tab = tab_widget.widget(0)
                for j in range(basic_tab.layout().count()):
                    item = basic_tab.layout().itemAt(j)
                    if item.widget() and isinstance(item.widget(), QGroupBox):
                        group_box = item.widget()
                        if 'quotation_details' in group_box.title().lower() or '報價單詳細' in group_box.title():
                            group_box.setTitle(self.lang_manager.get_text('quotation_details'))
                            # 更新表單標籤
                            form_layout = group_box.layout()
                            for k in range(form_layout.rowCount()):
                                label_item = form_layout.itemAt(k, QFormLayout.LabelRole)
                                if label_item and label_item.widget():
                                    label = label_item.widget()
                                    if isinstance(label, QLabel):
                                        text = label.text()
                                        if 'quotation no' in text.lower() or '報價單號' in text:
                                            label.setText(self.lang_manager.get_text('quotation_no'))
                                        elif 'date' in text.lower() or '日期' in text:
                                            label.setText(self.lang_manager.get_text('date'))
                                        elif 'valid until' in text.lower() or '有效期至' in text:
                                            label.setText(self.lang_manager.get_text('valid_until'))
                                        elif 'status' in text.lower() or '狀態' in text:
                                            label.setText(self.lang_manager.get_text('status'))
                        elif 'customer_info' in group_box.title().lower() or '客戶信息' in group_box.title():
                            group_box.setTitle(self.lang_manager.get_text('customer_info'))
                            # 更新表單標籤
                            form_layout = group_box.layout()
                            for k in range(form_layout.rowCount()):
                                label_item = form_layout.itemAt(k, QFormLayout.LabelRole)
                                if label_item and label_item.widget():
                                    label = label_item.widget()
                                    if isinstance(label, QLabel):
                                        text = label.text()
                                        if 'customer' in text.lower() or '客戶' in text:
                                            label.setText(self.lang_manager.get_text('customer'))
                                        elif 'contact person' in text.lower() or '聯絡人' in text:
                                            label.setText(self.lang_manager.get_text('contact_person'))
                                        elif 'phone' in text.lower() or '電話' in text:
                                            label.setText(self.lang_manager.get_text('phone'))
                                        elif 'address' in text.lower() or '地址' in text:
                                            label.setText(self.lang_manager.get_text('address'))
                
                # 更新項目頁
                items_tab = tab_widget.widget(1)
                if items_tab:
                    # 更新按鈕文字
                    for j in range(items_tab.layout().count()):
                        item = items_tab.layout().itemAt(j)
                        if item and item.layout():
                            for k in range(item.layout().count()):
                                btn_item = item.layout().itemAt(k)
                                if btn_item and btn_item.widget() and isinstance(btn_item.widget(), QPushButton):
                                    btn = btn_item.widget()
                                    if 'add' in btn.text().lower() or '新增' in btn.text():
                                        btn.setText(self.lang_manager.get_text('add_item'))
                                    elif 'edit' in btn.text().lower() or '編輯' in btn.text():
                                        btn.setText(self.lang_manager.get_text('edit_item'))
                                    elif 'delete' in btn.text().lower() or '刪除' in btn.text():
                                        btn.setText(self.lang_manager.get_text('delete_item'))
                    
                    # 更新表格標題
                    for j in range(items_tab.layout().count()):
                        item = items_tab.layout().itemAt(j)
                        if item and item.widget() and isinstance(item.widget(), QTableWidget):
                            table = item.widget()
                            if table.columnCount() == 7:  # 判斷是否為項目表格
                                table.setHorizontalHeaderLabels([
                                    self.lang_manager.get_text('item_no'),
                                    self.lang_manager.get_text('description'),
                                    self.lang_manager.get_text('quantity'),
                                    self.lang_manager.get_text('unit'),
                                    self.lang_manager.get_text('unit_price'),
                                    self.lang_manager.get_text('discount'),
                                    self.lang_manager.get_text('amount')
                                ])
                    
                    # 更新合計區域標籤
                    for j in range(items_tab.layout().count()):
                        item = items_tab.layout().itemAt(j)
                        if item and item.layout() and isinstance(item.layout(), QFormLayout):
                            form_layout = item.layout()
                            for k in range(form_layout.rowCount()):
                                label_item = form_layout.itemAt(k, QFormLayout.LabelRole)
                                if label_item and label_item.widget():
                                    label = label_item.widget()
                                    if isinstance(label, QLabel):
                                        text = label.text()
                                        if 'subtotal' in text.lower() or '小計' in text:
                                            label.setText(self.lang_manager.get_text('subtotal'))
                                        elif 'discount' in text.lower() or '折扣' in text:
                                            label.setText(self.lang_manager.get_text('discount'))
                                        elif 'total' in text.lower() or '總計' in text:
                                            label.setText(self.lang_manager.get_text('total'))
                
                # 更新備註和條款頁
                notes_tab = tab_widget.widget(2)
                if notes_tab:
                    for j in range(notes_tab.layout().count()):
                        item = notes_tab.layout().itemAt(j)
                        if item.widget() and isinstance(item.widget(), QGroupBox):
                            group_box = item.widget()
                            if 'notes' in group_box.title().lower() or '備註' in group_box.title():
                                group_box.setTitle(self.lang_manager.get_text('notes'))
                            elif 'terms' in group_box.title().lower() or '條款' in group_box.title():
                                group_box.setTitle(self.lang_manager.get_text('terms_and_conditions'))
        
        # 更新狀態下拉框選項
        current_status = self.status_combo.currentText()
        self.status_combo.clear()
        self.status_combo.addItem(self.lang_manager.get_text('draft'))
        self.status_combo.addItem(self.lang_manager.get_text('sent'))
        self.status_combo.addItem(self.lang_manager.get_text('approved'))
        self.status_combo.addItem(self.lang_manager.get_text('rejected'))
        
        # 嘗試恢復之前選擇的狀態
        status_index = 0
        if current_status == self.lang_manager.get_text('draft') or current_status == 'Draft':
            status_index = 0
        elif current_status == self.lang_manager.get_text('sent') or current_status == 'Sent':
            status_index = 1
        elif current_status == self.lang_manager.get_text('approved') or current_status == 'Approved':
            status_index = 2
        elif current_status == self.lang_manager.get_text('rejected') or current_status == 'Rejected':
            status_index = 3
        self.status_combo.setCurrentIndex(status_index)
        
        # 更新按鈕文字
        self.btn_save.setText(self.lang_manager.get_text('save'))
        self.btn_preview.setText(self.lang_manager.get_text('preview'))
        self.btn_export_pdf.setText(self.lang_manager.get_text('export_pdf'))
        self.btn_cancel.setText(self.lang_manager.get_text('cancel'))
        
        # 如果有必要，重新整理項目表格
        self.refresh_items_table()

    def export_pdf(self):
        """實現匯出PDF功能"""
        try:
            # 獲取表單數據
            quotation_data = self.get_data()
            
            # 準備默認文件名
            default_filename = f"Quotation_{quotation_data['quotation_no'].replace('/', '_')}.pdf"
            
            # 顯示文件保存對話框
            from PyQt5.QtWidgets import QFileDialog
            output_path, _ = QFileDialog.getSaveFileName(
                self,
                self.lang_manager.get_text('export_pdf'),
                default_filename,
                "PDF Files (*.pdf)"
            )
            
            # 如果用戶取消，則返回
            if not output_path:
                return
            
            # 使用統一的PDF管理器
            from app.utils.pdf_manager import PDFManager
            pdf_manager = PDFManager()
            pdf_file = pdf_manager.generate_quotation_pdf(quotation_data, output_path)
            
            if pdf_file:
                # 顯示成功消息
                QMessageBox.information(
                    self, 
                    self.lang_manager.get_text('success'),
                    f"{self.lang_manager.get_text('export_success')}\n{pdf_file}"
                )
        except ImportError:
            QMessageBox.information(
                self, 
                self.lang_manager.get_text('information'),
                self.lang_manager.get_text('export_pdf_not_implemented')
            )
        except Exception as e:
            QMessageBox.warning(
                self, 
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('export_pdf_error')}: {str(e)}"
            )


class QuotationItemDialog(QDialog):
    """報價單項目對話框"""
    
    def __init__(self, parent=None, db_manager=None, item_data=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.item_data = item_data  # 如果提供，則為編輯模式
        self.lang_manager = LanguageManager()
        
        self.init_ui()
        
        # 如果是編輯模式，載入項目數據
        if self.item_data:
            self.load_item_data()
    
    def init_ui(self):
        """初始化界面"""
        # 設置窗口屬性
        self.setWindowTitle(self.lang_manager.get_text('add_item') if not self.item_data else self.lang_manager.get_text('edit_item'))
        self.setMinimumWidth(500)
        self.setMinimumHeight(450)
        
        # 主佈局
        main_layout = QVBoxLayout(self)
        
        # 項目類型選擇
        type_group = QGroupBox(self.lang_manager.get_text('item_type'))
        type_layout = QHBoxLayout(type_group)
        
        self.product_radio = QRadioButton(self.lang_manager.get_text('product'))
        self.service_radio = QRadioButton(self.lang_manager.get_text('service'))
        
        type_layout.addWidget(self.product_radio)
        type_layout.addWidget(self.service_radio)
        
        # 默認選擇產品
        self.product_radio.setChecked(True)
        
        # 連接信號槽
        self.product_radio.toggled.connect(self.toggle_item_type)
        
        main_layout.addWidget(type_group)
        
        # 產品表單
        self.product_widget = QWidget()
        product_layout = QFormLayout(self.product_widget)
        
        # 產品選擇
        self.product_combo = QComboBox()
        self.product_combo.setEditable(True)
        self.product_combo.setMinimumWidth(250)
        product_layout.addRow(self.lang_manager.get_text('product'), self.product_combo)
        
        # 產品描述
        self.product_description_edit = QLineEdit()
        product_layout.addRow(self.lang_manager.get_text('description'), self.product_description_edit)
        
        main_layout.addWidget(self.product_widget)
        
        # 服務表單
        self.service_widget = QWidget()
        service_layout = QFormLayout(self.service_widget)
        
        # 服務名稱
        self.service_name_edit = QLineEdit()
        service_layout.addRow(self.lang_manager.get_text('service_name'), self.service_name_edit)
        
        # 服務描述（更大的輸入區域）
        self.service_description_edit = QTextEdit()
        self.service_description_edit.setMinimumHeight(150)  # 設置更大的高度
        service_layout.addRow(self.lang_manager.get_text('service_description'), self.service_description_edit)
        
        main_layout.addWidget(self.service_widget)
        
        # 共用表單
        common_layout = QFormLayout()
        
        # 數量
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.01, 9999.99)
        self.quantity_spin.setValue(1)
        common_layout.addRow(self.lang_manager.get_text('quantity'), self.quantity_spin)
        
        # 單位
        self.unit_edit = QLineEdit()
        self.unit_edit.setText("個")
        common_layout.addRow(self.lang_manager.get_text('unit'), self.unit_edit)
        
        # 單價
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 9999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setPrefix("$")
        common_layout.addRow(self.lang_manager.get_text('unit_price'), self.unit_price_spin)
        
        # 折扣
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix("%")
        common_layout.addRow(self.lang_manager.get_text('discount'), self.discount_spin)
        
        main_layout.addLayout(common_layout)
        
        # 初始顯示產品，隱藏服務
        self.toggle_item_type()
        
        # 添加分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        main_layout.addWidget(separator)
        
        # 按鈕區域
        buttons_layout = QHBoxLayout()
        
        self.btn_save = QPushButton(self.lang_manager.get_text('save'))
        self.btn_cancel = QPushButton(self.lang_manager.get_text('cancel'))
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_cancel)
        
        main_layout.addLayout(buttons_layout)
        
        # 連接事件
        self.btn_save.clicked.connect(self.accept)
        self.btn_cancel.clicked.connect(self.reject)
        self.product_combo.currentIndexChanged.connect(self.on_product_changed)
        
        # 加載產品列表
        self.load_products()
    
    def toggle_item_type(self):
        """切換項目類型顯示"""
        is_product = self.product_radio.isChecked()
        self.product_widget.setVisible(is_product)
        self.service_widget.setVisible(not is_product)
    
    def load_products(self):
        """加載產品列表到下拉框"""
        if self.db_manager:
            products = self.db_manager.get_products()
            self.product_combo.clear()
            
            # 添加一個空選項
            self.product_combo.addItem("", None)
            
            # 添加產品
            product_names = []
            for product in products:
                self.product_combo.addItem(product['name'], product['id'])
                product_names.append(product['name'])
            
            # 添加自動完成功能
            completer = QCompleter(product_names)
            completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.product_combo.setCompleter(completer)
    
    def on_product_changed(self, index):
        """當產品選擇變更時，更新描述、單位和單價"""
        if index <= 0:  # 未選擇產品
            self.product_description_edit.clear()
            self.unit_edit.setText("個")
            self.unit_price_spin.setValue(0)
            return
            
        product_id = self.product_combo.itemData(index)
        product = self.db_manager.get_product(product_id)
        
        if product:
            # 使用字典格式訪問產品資料
            description = f"{product.get('name', '')}"
            if product.get('specification'):  # 如果有規格
                description += f" ({product.get('specification')})"
            
            self.product_description_edit.setText(description)
            self.unit_edit.setText(product.get('unit', '個'))
            self.unit_price_spin.setValue(product.get('price', 0))
    
    def load_item_data(self):
        """載入項目數據（編輯模式）"""
        if not self.item_data:
            return
            
        # 判斷是產品還是服務
        is_product = self.item_data.get('product_id') is not None
        
        # 設置項目類型
        if is_product:
            self.product_radio.setChecked(True)
            # 設置產品
            if 'product_id' in self.item_data:
                for i in range(self.product_combo.count()):
                    if self.product_combo.itemData(i) == self.item_data['product_id']:
                        self.product_combo.setCurrentIndex(i)
                        break
            
            # 設置產品描述
            self.product_description_edit.setText(self.item_data.get('description', ''))
        else:
            self.service_radio.setChecked(True)
            # 設置服務名稱和描述
            service_name = self.item_data.get('description', '').split('\n')[0] if '\n' in self.item_data.get('description', '') else self.item_data.get('description', '')
            self.service_name_edit.setText(service_name)
            
            # 如果描述中有多行，把第一行之後的內容作為服務描述
            if '\n' in self.item_data.get('description', ''):
                service_desc = '\n'.join(self.item_data.get('description', '').split('\n')[1:])
                self.service_description_edit.setText(service_desc)
        
        # 設置共用項目詳情
        self.quantity_spin.setValue(self.item_data.get('quantity', 1))
        self.unit_edit.setText(self.item_data.get('unit', '個'))
        self.unit_price_spin.setValue(self.item_data.get('unit_price', 0))
        self.discount_spin.setValue(self.item_data.get('discount', 0))
        
        # 切換顯示
        self.toggle_item_type()
    
    def get_data(self):
        """獲取對話框的數據"""
        is_product = self.product_radio.isChecked()
        
        if is_product:
            # 產品項目
            product_index = self.product_combo.currentIndex()
            product_id = self.product_combo.itemData(product_index) if product_index > 0 else None
            description = self.product_description_edit.text()
            name = self.product_combo.currentText() if product_index > 0 else ''
        else:
            # 服務項目
            product_id = None
            name = self.service_name_edit.text()
            
            # 服務描述可能有多行，將服務名稱和描述合併
            service_description = self.service_description_edit.toPlainText()
            if service_description:
                description = f"{name}\n{service_description}"
            else:
                description = name
        
        data = {
            'product_id': product_id,
            'product_name': name,
            'description': description,
            'quantity': self.quantity_spin.value(),
            'unit': self.unit_edit.text(),
            'unit_price': self.unit_price_spin.value(),
            'discount': self.discount_spin.value()
        }
        
        return data
        
    def refresh_translations(self):
        """刷新界面翻譯（當語言改變時調用）"""
        self.lang_manager = LanguageManager()  # 重新加載語言設定
        
        # 更新窗口標題
        self.setWindowTitle(self.lang_manager.get_text('add_item') if not self.item_data else self.lang_manager.get_text('edit_item'))
        
        # 更新項目類型群組標題
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.widget() and isinstance(item.widget(), QGroupBox):
                group_box = item.widget()
                if 'item_type' in group_box.title().lower() or '項目類型' in group_box.title():
                    group_box.setTitle(self.lang_manager.get_text('item_type'))
        
        # 更新項目類型
        self.product_radio.setText(self.lang_manager.get_text('product'))
        self.service_radio.setText(self.lang_manager.get_text('service'))
        
        # 更新產品區域表單標籤
        product_layout = self.product_widget.layout()
        if product_layout:
            for i in range(product_layout.rowCount()):
                label_item = product_layout.itemAt(i, QFormLayout.LabelRole)
                if label_item and label_item.widget():
                    label = label_item.widget()
                    if isinstance(label, QLabel):
                        text = label.text()
                        if 'product' in text.lower() or '產品' in text:
                            label.setText(self.lang_manager.get_text('product'))
                        elif 'description' in text.lower() or '描述' in text:
                            label.setText(self.lang_manager.get_text('description'))
        
        # 更新服務區域表單標籤
        service_layout = self.service_widget.layout()
        if service_layout:
            for i in range(service_layout.rowCount()):
                label_item = service_layout.itemAt(i, QFormLayout.LabelRole)
                if label_item and label_item.widget():
                    label = label_item.widget()
                    if isinstance(label, QLabel):
                        text = label.text()
                        if 'service_name' in text.lower() or '服務名稱' in text:
                            label.setText(self.lang_manager.get_text('service_name'))
                        elif 'service_description' in text.lower() or '服務描述' in text:
                            label.setText(self.lang_manager.get_text('service_description'))
        
        # 更新共用表單標籤
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.layout() and isinstance(item.layout(), QFormLayout):
                form_layout = item.layout()
                for j in range(form_layout.rowCount()):
                    label_item = form_layout.itemAt(j, QFormLayout.LabelRole)
                    if label_item and label_item.widget():
                        label = label_item.widget()
                        if isinstance(label, QLabel):
                            text = label.text()
                            if 'quantity' in text.lower() or '數量' in text:
                                label.setText(self.lang_manager.get_text('quantity'))
                            elif 'unit' in text.lower() or '單位' in text:
                                label.setText(self.lang_manager.get_text('unit'))
                            elif 'unit price' in text.lower() or '單價' in text:
                                label.setText(self.lang_manager.get_text('unit_price'))
                            elif 'discount' in text.lower() or '折扣' in text:
                                label.setText(self.lang_manager.get_text('discount'))
        
        # 更新按鈕文字
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if item and item.layout():
                for j in range(item.layout().count()):
                    btn_item = item.layout().itemAt(j)
                    if btn_item and btn_item.widget() and isinstance(btn_item.widget(), QPushButton):
                        btn = btn_item.widget()
                        if 'save' in btn.text().lower() or '儲存' in btn.text():
                            btn.setText(self.lang_manager.get_text('save'))
                        elif 'cancel' in btn.text().lower() or '取消' in btn.text():
                            btn.setText(self.lang_manager.get_text('cancel')) 