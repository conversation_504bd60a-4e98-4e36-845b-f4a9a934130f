from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QFileDialog, QMessageBox, QGroupBox, QCheckBox,
                             QFrame, QProgressBar, QApplication)
from PyQt5.QtCore import Qt, QCoreApplication
import os
import sqlite3
import json
import shutil
import datetime
from app.utils.language_manager import LanguageManager

class BackupDialog(QDialog):
    """備份數據對話框"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.lang_manager = LanguageManager()
        self.backup_path = None
        self.selected_tables = []
        
        # 設置對話框屬性
        self.setWindowTitle(self.lang_manager.get_text('data_backup'))
        self.resize(500, 400)
        self.setModal(True)
        
        # 初始化界面
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        
        # 標題
        title_label = QLabel(self.lang_manager.get_text('data_backup'))
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 分隔線
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 說明文字
        description = QLabel(self.lang_manager.get_text('backup_description'))
        description.setWordWrap(True)
        main_layout.addWidget(description)
        
        # 選擇資料表區域
        tables_group = QGroupBox(self.lang_manager.get_text('select_tables'))
        tables_layout = QVBoxLayout(tables_group)
        
        # 添加全選/取消全選按鈕
        select_buttons_layout = QHBoxLayout()
        select_all_btn = QPushButton(self.lang_manager.get_text('select_all'))
        deselect_all_btn = QPushButton(self.lang_manager.get_text('deselect_all'))
        select_all_btn.clicked.connect(self.select_all_tables)
        deselect_all_btn.clicked.connect(self.deselect_all_tables)
        select_buttons_layout.addWidget(select_all_btn)
        select_buttons_layout.addWidget(deselect_all_btn)
        tables_layout.addLayout(select_buttons_layout)
        
        # 添加資料表選擇框
        self.table_checkboxes = {}
        
        # 獲取所有資料表信息
        self.tables_info = [
            {"name": "customers", "display_name": self.lang_manager.get_text('customers_table')},
            {"name": "products", "display_name": self.lang_manager.get_text('products_table')},
            {"name": "quotations", "display_name": self.lang_manager.get_text('quotations_table')},
            {"name": "quotation_items", "display_name": self.lang_manager.get_text('quotation_items_table')},
            {"name": "invoices", "display_name": self.lang_manager.get_text('invoices_table')},
            {"name": "invoice_items", "display_name": self.lang_manager.get_text('invoice_items_table')},
            {"name": "delivery_notes", "display_name": self.lang_manager.get_text('delivery_notes_table')},
            {"name": "delivery_items", "display_name": self.lang_manager.get_text('delivery_items_table')},
            {"name": "users", "display_name": self.lang_manager.get_text('users_table')}
        ]
        
        for table_info in self.tables_info:
            checkbox = QCheckBox(table_info["display_name"])
            checkbox.setChecked(True)  # 默認全選
            self.table_checkboxes[table_info["name"]] = checkbox
            tables_layout.addWidget(checkbox)
        
        main_layout.addWidget(tables_group)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 按鈕區域
        buttons_layout = QHBoxLayout()
        self.backup_btn = QPushButton(self.lang_manager.get_text('start_backup'))
        self.backup_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.backup_btn.clicked.connect(self.start_backup)
        
        self.cancel_btn = QPushButton(self.lang_manager.get_text('cancel'))
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.backup_btn)
        buttons_layout.addWidget(self.cancel_btn)
        main_layout.addLayout(buttons_layout)
    
    def select_all_tables(self):
        """選擇所有資料表"""
        for checkbox in self.table_checkboxes.values():
            checkbox.setChecked(True)
    
    def deselect_all_tables(self):
        """取消選擇所有資料表"""
        for checkbox in self.table_checkboxes.values():
            checkbox.setChecked(False)
    
    def get_selected_tables(self):
        """獲取選中的資料表"""
        return [name for name, checkbox in self.table_checkboxes.items() if checkbox.isChecked()]
    
    def start_backup(self):
        """開始備份流程"""
        # 確認要備份的表格
        selected_tables = self.get_selected_tables()
        
        if not selected_tables:
            QMessageBox.warning(
                self,
                self.lang_manager.get_text('warning'),
                self.lang_manager.get_text('no_tables_selected')
            )
            return
        
        # 選擇備份保存位置
        backup_path, _ = QFileDialog.getSaveFileName(
            self,
            self.lang_manager.get_text('select_backup_location'),
            f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.hbbackup",
            self.lang_manager.get_text('backup_files') + " (*.hbbackup)"
        )
        
        if not backup_path:
            return  # 用戶取消了保存對話框
        
        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 禁用按鈕，避免重複點擊
        self.backup_btn.setEnabled(False)
        self.cancel_btn.setEnabled(False)
        
        try:
            # 執行備份操作
            self.execute_backup(selected_tables, backup_path)
            
            # 備份完成
            QMessageBox.information(
                self,
                self.lang_manager.get_text('success'),
                self.lang_manager.get_text('backup_completed') + f"\n{backup_path}"
            )
            
            self.accept()  # 關閉對話框
            
        except Exception as e:
            QMessageBox.critical(
                self,
                self.lang_manager.get_text('error'),
                f"{self.lang_manager.get_text('backup_failed')}\n{str(e)}"
            )
            
            # 重新啟用按鈕
            self.backup_btn.setEnabled(True)
            self.cancel_btn.setEnabled(True)
            # 隱藏進度條
            self.progress_bar.setVisible(False)
    
    def execute_backup(self, selected_tables, backup_path):
        """執行備份操作"""
        try:
            # 建立數據庫連接
            if not self.db_manager.connect():
                raise Exception(self.lang_manager.get_text('database_connection_error'))
                
            # 備份數據
            backup_data = {}
            total_tables = len(selected_tables)
            
            for i, table_name in enumerate(selected_tables):
                # 更新進度條
                self.progress_bar.setValue(int((i / total_tables) * 100))
                QApplication.processEvents()  # 確保UI更新
                
                # 獲取表格結構
                self.db_manager.cursor.execute(f"PRAGMA table_info({table_name})")
                table_info = self.db_manager.cursor.fetchall()
                columns = [col[1] for col in table_info]
                
                # 獲取表格數據
                self.db_manager.cursor.execute(f"SELECT * FROM {table_name}")
                rows = self.db_manager.cursor.fetchall()
                
                # 將數據轉換為字典列表
                table_data = []
                for row in rows:
                    row_dict = {}
                    for j, col in enumerate(columns):
                        row_dict[col] = row[j]
                    table_data.append(row_dict)
                
                # 添加到備份數據
                backup_data[table_name] = {
                    "schema": table_info,
                    "data": table_data
                }
            
            # 關閉數據庫連接
            self.db_manager.disconnect()
            
            # 將數據寫入備份文件
            with open(backup_path, 'w', encoding='utf-8') as backup_file:
                backup_metadata = {
                    "backup_date": datetime.datetime.now().isoformat(),
                    "backup_version": "1.0",
                    "tables": selected_tables
                }
                
                json_data = {
                    "metadata": backup_metadata,
                    "data": backup_data
                }
                
                json.dump(json_data, backup_file, ensure_ascii=False, indent=4)
            
            # 完成進度條
            self.progress_bar.setValue(100)
            
        except Exception as e:
            # 確保數據庫連接關閉
            if self.db_manager.conn:
                self.db_manager.disconnect()
            raise e 